import { ElNotification } from "element-plus";

/**
 * @description 接收数据流生成 blob，创建链接，下载文件
 * @param {Function} api 导出表格的api方法 (必传)
 * @param {String} tempName 导出的文件名 (必传)
 * @param {Object} params 导出的参数 (默认{})
 * @param {Boolean} isNotify 是否有导出消息提示 (默认为 true)
 * @param {String} fileType 导出的文件格式 (默认为.xlsx)
 * */
export const useDownload = async (api, tempName, params = {}, isNotify = true, fileType = ".xlsx") => {
  if (isNotify) {
    ElNotification({
      title: "温馨提示",
      message: "如果数据庞大会导致下载缓慢哦，请您耐心等待！",
      type: "info",
      duration: 3000
    });
  }
  try {
    const res = await api(params);
    console.log('下载响应:', res);

    // 处理不同的响应格式
    let blob;
    if (res.data instanceof Blob) {
      // 如果响应已经是Blob格式
      blob = res.data;
    } else if (res instanceof Blob) {
      // 如果响应本身就是Blob
      blob = res;
    } else if (res.data) {
      // 如果响应包含data字段
      blob = new Blob([res.data]);
    } else {
      // 其他情况
      blob = new Blob([res]);
    }

    // 检查blob是否为空
    if (blob.size === 0) {
      ElNotification({
        title: "下载失败",
        message: "文件内容为空，请检查服务器配置或联系管理员",
        type: "error",
        duration: 5000
      });
      return;
    }

    // 兼容 edge 不支持 createObjectURL 方法
    if ("msSaveOrOpenBlob" in navigator) return window.navigator.msSaveOrOpenBlob(blob, tempName + fileType);
    const blobUrl = window.URL.createObjectURL(blob);
    const exportFile = document.createElement("a");
    exportFile.style.display = "none";
    exportFile.download = `${tempName}${fileType}`;
    exportFile.href = blobUrl;
    document.body.appendChild(exportFile);
    exportFile.click();
    // 去除下载对 url 的影响
    document.body.removeChild(exportFile);
    window.URL.revokeObjectURL(blobUrl);

    if (isNotify) {
      ElNotification({
        title: "下载成功",
        message: `${tempName}${fileType} 下载完成`,
        type: "success",
        duration: 3000
      });
    }
  } catch (error) {
    console.error('下载失败:', error);
    ElNotification({
      title: "下载失败",
      message: error.message || "下载过程中发生错误，请重试",
      type: "error",
      duration: 5000
    });
  }
};
