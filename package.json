{"name": "wujin-jnl", "private": true, "version": "1.0.0", "type": "module", "description": "wujin-jnl", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lw123lw"}, "license": "MIT", "homepage": "https://github.com/lw123lw/Geeker-Admin-JS", "repository": {"type": "git", "url": "**************:lw123lw/Geeker-Admin-JS.git"}, "bugs": {"url": "https://github.com/lw123lw/Geeker-Admin-JS/issues"}, "scripts": {"dev": "vite", "serve": "vite", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "build:pro": "vite build --mode production", "type:check": "vue --noEmit --skip<PERSON><PERSON><PERSON><PERSON><PERSON>", "preview": "pnpm build:dev && vite preview", "lint:prettier": "prettier --write \"src/**/*.{js,json,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "prepare": "husky install", "release": "standard-version", "commit": "git add -A && czg && git push"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^10.11.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "ant-design-vue": "^4.2.6", "axios": "^1.7.2", "dayjs": "^1.11.11", "driver.js": "^1.3.1", "echarts": "^5.5.1", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.8.0", "md5": "^2.3.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "qs": "^6.12.1", "relation-graph-vue3": "^2.2.4", "screenfull": "^6.0.2", "socket.io-client": "^4.7.5", "sortablejs": "^1.15.2", "vue": "^3.4.31", "vue-i18n": "^9.13.1", "vue-router": "^4.4.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.19", "babel-cli": "^6.26.0", "cz-git": "1.9.2", "czg": "^1.9.2", "husky": "^9.0.11", "lint-staged": "^15.2.5", "postcss": "^8.4.38", "postcss-html": "^1.7.0", "prettier": "^3.3.2", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.77.6", "standard-version": "^9.5.0", "stylelint": "^16.6.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^5.0.1", "stylelint-config-recommended-scss": "^14.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.0", "stylelint-config-standard-scss": "^13.1.0", "unplugin-vue-setup-extend-plus": "^1.0.1", "vite": "^5.3.2", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-pwa": "^0.20.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.3.5"}, "engines": {"node": ">=16.18.0"}, "browserslist": {"production": ["> 1%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}