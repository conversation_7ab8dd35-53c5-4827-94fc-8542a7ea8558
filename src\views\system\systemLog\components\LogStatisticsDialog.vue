<template>
  <el-dialog
    v-model="dialogVisible"
    title="日志统计"
    width="900px"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <div class="statistics-container">
      <!-- 统计卡片 -->
      <div class="statistics-cards">
        <el-card shadow="hover" class="statistics-card">
          <template #header>
            <div class="card-header">
              <span>总操作数</span>
              <el-tag type="info">{{ statisticsData.days }}天</el-tag>
            </div>
          </template>
          <div class="card-value">{{ statisticsData.totalOperations }}</div>
        </el-card>
        
        <el-card shadow="hover" class="statistics-card">
          <template #header>
            <div class="card-header">
              <span>成功操作</span>
            </div>
          </template>
          <div class="card-value success">{{ statisticsData.successOperations }}</div>
        </el-card>
        
        <el-card shadow="hover" class="statistics-card">
          <template #header>
            <div class="card-header">
              <span>失败操作</span>
            </div>
          </template>
          <div class="card-value danger">{{ statisticsData.failedOperations }}</div>
        </el-card>
        
        <el-card shadow="hover" class="statistics-card">
          <template #header>
            <div class="card-header">
              <span>成功率</span>
            </div>
          </template>
          <div class="card-value">{{ statisticsData.successRate }}%</div>
        </el-card>
      </div>
      
      <!-- 图表区域 -->
      <div class="statistics-charts">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="每日统计" name="daily">
            <div class="chart-container">
              <div ref="dailyChartRef" class="chart"></div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="模块统计" name="module">
            <div class="chart-container">
              <div ref="moduleChartRef" class="chart"></div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="操作类型" name="operation">
            <div class="chart-container">
              <div ref="operationChartRef" class="chart"></div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <!-- 统计设置 -->
      <div class="statistics-settings">
        <el-form :model="queryParams" label-width="80px" inline>
          <el-form-item label="统计天数">
            <el-select v-model="queryParams.days" @change="fetchStatistics">
              <el-option label="最近7天" :value="7" />
              <el-option label="最近15天" :value="15" />
              <el-option label="最近30天" :value="30" />
              <el-option label="最近90天" :value="90" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="refreshStatistics">刷新数据</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { ElMessage } from "element-plus";
import * as echarts from "echarts";
import { getSystemLogStatistics } from "@/api/modules/systemLog";

// 对话框可见性
const dialogVisible = ref(false);

// 当前激活的标签页
const activeTab = ref("daily");

// 图表引用
const dailyChartRef = ref(null);
const moduleChartRef = ref(null);
const operationChartRef = ref(null);

// 图表实例
let dailyChart = null;
let moduleChart = null;
let operationChart = null;

// 查询参数
const queryParams = reactive({
  days: 7
});

// 统计数据
const statisticsData = reactive({
  days: 7,
  totalOperations: 0,
  successOperations: 0,
  failedOperations: 0,
  successRate: 0,
  moduleStats: {},
  dailyStats: {},
  operationTypeStats: {}
});

/**
 * 获取统计数据
 */
const fetchStatistics = async () => {
  try {
    const { data } = await getSystemLogStatistics({ days: queryParams.days });
    
    // 更新统计数据
    Object.assign(statisticsData, data);
    statisticsData.days = queryParams.days;
    
    // 更新图表
    nextTick(() => {
      initCharts();
    });
  } catch (error) {
    ElMessage.error("获取统计数据失败");
  }
};

/**
 * 刷新统计数据
 */
const refreshStatistics = () => {
  fetchStatistics();
  ElMessage.success("统计数据已刷新");
};

/**
 * 初始化图表
 */
const initCharts = () => {
  initDailyChart();
  initModuleChart();
  initOperationChart();
};

/**
 * 初始化每日统计图表
 */
const initDailyChart = () => {
  if (!dailyChartRef.value) return;
  
  // 销毁旧图表
  if (dailyChart) {
    dailyChart.dispose();
  }
  
  // 创建新图表
  dailyChart = echarts.init(dailyChartRef.value);
  
  // 处理数据
  const days = Object.keys(statisticsData.dailyStats).sort();
  const values = days.map(day => statisticsData.dailyStats[day]);
  
  // 设置图表选项
  const option = {
    title: {
      text: '每日操作统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: days,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '操作次数',
        type: 'bar',
        data: values,
        itemStyle: {
          color: '#409EFF'
        }
      }
    ]
  };
  
  // 设置图表
  dailyChart.setOption(option);
};

/**
 * 初始化模块统计图表
 */
const initModuleChart = () => {
  if (!moduleChartRef.value) return;
  
  // 销毁旧图表
  if (moduleChart) {
    moduleChart.dispose();
  }
  
  // 创建新图表
  moduleChart = echarts.init(moduleChartRef.value);
  
  // 处理数据
  const modules = Object.keys(statisticsData.moduleStats);
  const data = modules.map(module => ({
    name: module,
    value: statisticsData.moduleStats[module]
  }));
  
  // 设置图表选项
  const option = {
    title: {
      text: '模块操作统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: modules
    },
    series: [
      {
        name: '模块统计',
        type: 'pie',
        radius: '60%',
        center: ['50%', '60%'],
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
  
  // 设置图表
  moduleChart.setOption(option);
};

/**
 * 初始化操作类型统计图表
 */
const initOperationChart = () => {
  if (!operationChartRef.value) return;
  
  // 销毁旧图表
  if (operationChart) {
    operationChart.dispose();
  }
  
  // 创建新图表
  operationChart = echarts.init(operationChartRef.value);
  
  // 处理数据
  const types = Object.keys(statisticsData.operationTypeStats);
  const data = types.map(type => ({
    name: type,
    value: statisticsData.operationTypeStats[type]
  }));
  
  // 设置图表选项
  const option = {
    title: {
      text: '操作类型统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: types
    },
    series: [
      {
        name: '操作类型',
        type: 'pie',
        radius: '60%',
        center: ['50%', '60%'],
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
  
  // 设置图表
  operationChart.setOption(option);
};

/**
 * 打开对话框
 */
const open = () => {
  dialogVisible.value = true;
  // 获取统计数据
  fetchStatistics();
};

// 监听窗口大小变化，调整图表大小
window.addEventListener('resize', () => {
  dailyChart?.resize();
  moduleChart?.resize();
  operationChart?.resize();
});

// 暴露方法给父组件
defineExpose({
  open
});
</script>

<style scoped>
.statistics-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.statistics-cards {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.statistics-card {
  flex: 1;
  min-width: 180px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-value {
  font-size: 28px;
  font-weight: bold;
  text-align: center;
  padding: 10px 0;
}

.success {
  color: #67c23a;
}

.danger {
  color: #f56c6c;
}

.statistics-charts {
  min-height: 400px;
}

.chart-container {
  width: 100%;
  height: 400px;
}

.chart {
  width: 100%;
  height: 100%;
}

.statistics-settings {
  display: flex;
  justify-content: flex-end;
}
</style>
