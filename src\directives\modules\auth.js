/**
 * v-auth
 * 按钮权限指令 (基于权限等级)
 */
import { useAuthStore } from "@/stores/modules/auth";
import { hasPermission } from "@/config/permissions";

const auth = {
  mounted(el, binding) {
    const { value } = binding;
    const authStore = useAuthStore();
    const permissionLevel = authStore.permissionLevelGet;

    // 使用权限等级检查权限
    const hasAuth = hasPermission(permissionLevel, value);
    if (!hasAuth) {
      el.remove();
    }
  },
  updated(el, binding) {
    // 当权限等级更新时重新检查
    const { value } = binding;
    const authStore = useAuthStore();
    const permissionLevel = authStore.permissionLevelGet;

    const hasAuth = hasPermission(permissionLevel, value);
    if (!hasAuth) {
      el.remove();
    }
  }
};

export default auth;
