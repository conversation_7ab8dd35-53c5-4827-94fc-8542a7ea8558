<template>
  <div class="product-sales-chart">
    <div ref="productSalesChartRef" class="chart-container"></div>
  </div>
</template>

<script setup name="ProductSalesChart">
import { ref, onMounted, onBeforeUnmount } from "vue";
import * as echarts from "echarts";
import { getProductSalesDataApi } from "@/api/modules/dataScreen";

const productSalesChartRef = ref();
let myChart = null;

// 模拟产品销售数据
const mockData = [
  { name: '从动轮', value: 1548, percentage: 28.5 },
  { name: '主动轮', value: 1234, percentage: 22.7 },
  { name: '螺丝', value: 987, percentage: 18.2 },
  { name: '胶轮', value: 856, percentage: 15.8 },
  { name: '铁轮', value: 543, percentage: 10.0 },
  { name: '其他', value: 267, percentage: 4.8 }
];

const initChart = () => {
  myChart = echarts.init(productSalesChartRef.value);
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        return `${params.name}<br/>销量: ${params.value}<br/>占比: ${params.percent}%`;
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#667eea',
      borderWidth: 1,
      textStyle: {
        color: '#2c3e50'
      }
    },
    legend: {
      orient: 'horizontal',
      bottom: '5%',
      left: 'center',
      textStyle: {
        color: '#2c3e50',
        fontSize: 11
      },
      data: mockData.map(item => item.name)
    },
    series: [
      {
        name: '产品销售',
        type: 'pie',
        radius: ['40%', '75%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'outside',
          formatter: function(params) {
            return `${params.name}\n${params.percent}%`;
          },
          color: '#2c3e50',
          fontSize: 11
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10,
          lineStyle: {
            color: '#2c3e50'
          }
        },
        data: [
          {
            value: mockData[0].value,
            name: mockData[0].name,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 1,
                colorStops: [
                  { offset: 0, color: '#667eea' },
                  { offset: 1, color: '#764ba2' }
                ]
              }
            }
          },
          {
            value: mockData[1].value,
            name: mockData[1].name,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 1,
                colorStops: [
                  { offset: 0, color: '#f093fb' },
                  { offset: 1, color: '#f5576c' }
                ]
              }
            }
          },
          {
            value: mockData[2].value,
            name: mockData[2].name,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 1,
                colorStops: [
                  { offset: 0, color: '#4facfe' },
                  { offset: 1, color: '#00f2fe' }
                ]
              }
            }
          },
          {
            value: mockData[3].value,
            name: mockData[3].name,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 1,
                colorStops: [
                  { offset: 0, color: '#43e97b' },
                  { offset: 1, color: '#38f9d7' }
                ]
              }
            }
          },
          {
            value: mockData[4].value,
            name: mockData[4].name,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 1,
                colorStops: [
                  { offset: 0, color: '#fa709a' },
                  { offset: 1, color: '#fee140' }
                ]
              }
            }
          },
          {
            value: mockData[5].value,
            name: mockData[5].name,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 1,
                colorStops: [
                  { offset: 0, color: '#a8edea' },
                  { offset: 1, color: '#fed6e3' }
                ]
              }
            }
          }
        ]
      }
    ]
  };
  
  myChart.setOption(option);
};

const getProductSalesData = async () => {
  try {
    // 保留API调用接口，但暂时使用模拟数据
    // const { data } = await getProductSalesDataApi();
    console.log("获取产品销售数据:", mockData);
  } catch (error) {
    console.error("获取产品销售数据失败:", error);
  }
};

onMounted(() => {
  getProductSalesData();
  initChart();
  
  window.addEventListener("resize", () => {
    if (myChart) {
      myChart.resize();
    }
  });
});

onBeforeUnmount(() => {
  if (myChart) {
    myChart.dispose();
  }
});
</script>

<style scoped lang="scss">
.product-sales-chart {
  width: 100%;
  height: 100%;

  .chart-container {
    width: 100%;
    height: 100%;
    min-height: 200px;
  }
}
</style>
