<template>
  <div class="login-form-container">
    <!-- 登录/注册切换 -->
    <el-tabs v-model="activeTab" class="login-tabs" @tab-change="handleTabChange">
      <el-tab-pane label="登录" name="login">
        <!-- 登录表单 -->
        <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" size="large" @keyup.enter="handleLogin">
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名/手机号/邮箱"
              clearable
              maxlength="50"
            >
              <template #prefix>
                <el-icon class="el-input__icon">
                  <User />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              show-password
              autocomplete="new-password"
              clearable
              maxlength="32"
            >
              <template #prefix>
                <el-icon class="el-input__icon">
                  <Lock />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>

          <!-- 验证码功能暂时注释，根据后端需要可以启用 -->
          <!--
          <el-form-item v-if="needVerifyCode" prop="verifyCode">
            <div class="verify-code-container">
              <el-input
                v-model="loginForm.verifyCode"
                placeholder="请输入验证码"
                clearable
                maxlength="6"
                style="flex: 1; margin-right: 10px;"
              >
                <template #prefix>
                  <el-icon class="el-input__icon">
                    <Key />
                  </el-icon>
                </template>
              </el-input>
              <el-button
                :disabled="sendCodeDisabled"
                @click="sendVerifyCode('login')"
                style="width: 120px;"
              >
                {{ sendCodeText }}
              </el-button>
            </div>
          </el-form-item>
          -->

          <!-- 记住我 -->
          <el-form-item>
            <div class="login-options">
              <el-checkbox v-model="loginForm.rememberMe">记住我</el-checkbox>
              <el-link type="primary" @click="showResetPassword = true">忘记密码？</el-link>
            </div>
          </el-form-item>
        </el-form>

        <!-- 登录按钮 -->
        <div class="login-btn">
          <el-button
            :icon="UserFilled"
            size="large"
            type="primary"
            :loading="loading"
            @click="handleLogin"
            style="width: 100%;"
          >
            登录
          </el-button>
        </div>
      </el-tab-pane>

      <el-tab-pane label="注册" name="register">
        <!-- 注册表单 -->
        <el-form ref="registerFormRef" :model="registerForm" :rules="registerRules" size="large">
          <el-form-item prop="username">
            <el-input
              v-model="registerForm.username"
              placeholder="请输入用户名 (4-20位字母数字下划线)"
              clearable
              maxlength="20"
            >
              <template #prefix>
                <el-icon class="el-input__icon">
                  <User />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="mobile">
            <el-input
              v-model="registerForm.mobile"
              placeholder="请输入手机号"
              clearable
              maxlength="11"
            >
              <template #prefix>
                <el-icon class="el-input__icon">
                  <Iphone />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="email">
            <el-input
              v-model="registerForm.email"
              placeholder="请输入邮箱 (可选)"
              clearable
              maxlength="50"
            >
              <template #prefix>
                <el-icon class="el-input__icon">
                  <Message />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="realName">
            <el-input
              v-model="registerForm.realName"
              placeholder="请输入真实姓名 (可选)"
              clearable
              maxlength="50"
            >
              <template #prefix>
                <el-icon class="el-input__icon">
                  <Avatar />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              placeholder="请输入密码 (8-32位包含字母数字)"
              show-password
              autocomplete="new-password"
              clearable
              maxlength="32"
            >
              <template #prefix>
                <el-icon class="el-input__icon">
                  <Lock />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="confirmPassword">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="请确认密码"
              show-password
              autocomplete="new-password"
              clearable
              maxlength="32"
            >
              <template #prefix>
                <el-icon class="el-input__icon">
                  <Lock />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>

          <!-- 短信验证码 -->
          <el-form-item prop="smsCode">
            <div class="verify-code-container">
              <el-input
                v-model="registerForm.smsCode"
                placeholder="请输入短信验证码"
                clearable
                maxlength="6"
                style="flex: 1; margin-right: 10px;"
              >
                <template #prefix>
                  <el-icon class="el-input__icon">
                    <ChatDotRound />
                  </el-icon>
                </template>
              </el-input>
              <el-button
                :disabled="sendSmsCodeDisabled"
                @click="sendVerifyCode('register', 'sms')"
                style="width: 120px;"
              >
                {{ sendSmsCodeText }}
              </el-button>
            </div>
          </el-form-item>

          <!-- 邮箱验证码 (可选) -->
          <el-form-item v-if="registerForm.email" prop="emailCode">
            <div class="verify-code-container">
              <el-input
                v-model="registerForm.emailCode"
                placeholder="请输入邮箱验证码 (可选)"
                clearable
                maxlength="6"
                style="flex: 1; margin-right: 10px;"
              >
                <template #prefix>
                  <el-icon class="el-input__icon">
                    <Message />
                  </el-icon>
                </template>
              </el-input>
              <el-button
                :disabled="sendEmailCodeDisabled"
                @click="sendVerifyCode('register', 'email')"
                style="width: 120px;"
              >
                {{ sendEmailCodeText }}
              </el-button>
            </div>
          </el-form-item>
        </el-form>

        <!-- 注册按钮 -->
        <div class="login-btn">
          <el-button
            :icon="UserFilled"
            size="large"
            type="primary"
            :loading="registerLoading"
            @click="handleRegister"
            style="width: 100%;"
          >
            注册
          </el-button>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 重置密码对话框 -->
    <el-dialog v-model="showResetPassword" title="重置密码" width="400px" :close-on-click-modal="false">
      <el-form ref="resetFormRef" :model="resetForm" :rules="resetRules" size="large">
        <el-form-item prop="mobile">
          <el-input
            v-model="resetForm.mobile"
            placeholder="请输入手机号"
            clearable
            maxlength="11"
          >
            <template #prefix>
              <el-icon class="el-input__icon">
                <Iphone />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="smsCode">
          <div class="verify-code-container">
            <el-input
              v-model="resetForm.smsCode"
              placeholder="请输入短信验证码"
              clearable
              maxlength="6"
              style="flex: 1; margin-right: 10px;"
            >
              <template #prefix>
                <el-icon class="el-input__icon">
                  <ChatDotRound />
                </el-icon>
              </template>
            </el-input>
            <el-button
              :disabled="sendResetCodeDisabled"
              @click="sendVerifyCode('reset_password', 'sms')"
              style="width: 120px;"
            >
              {{ sendResetCodeText }}
            </el-button>
          </div>
        </el-form-item>

        <el-form-item prop="newPassword">
          <el-input
            v-model="resetForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
            autocomplete="new-password"
            clearable
            maxlength="32"
          >
            <template #prefix>
              <el-icon class="el-input__icon">
                <Lock />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showResetPassword = false">取消</el-button>
          <el-button type="primary" :loading="resetLoading" @click="handleResetPassword">
            重置密码
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="LoginForm">
import { ref, reactive, computed, onMounted, onBeforeUnmount } from "vue";
import { useRouter } from "vue-router";
import { HOME_URL } from "@/config";
import { ElNotification, ElMessage } from "element-plus";
import { useUserStore } from "@/stores/modules/user";
import { useTabsStore } from "@/stores/modules/tabs";
import { useKeepAliveStore } from "@/stores/modules/keepAlive";
import { initDynamicRouter } from "@/routers/modules/dynamicRouter";
import {
  Lock,
  User,
  UserFilled,
  Iphone,
  Message,
  Avatar,
  ChatDotRound
} from "@element-plus/icons-vue";
import { getTimeState } from "@/utils/index.js";

// ==================== 基础配置 ====================
const router = useRouter();
const userStore = useUserStore();
const tabsStore = useTabsStore();
const keepAliveStore = useKeepAliveStore();

// ==================== 响应式数据 ====================
const activeTab = ref("login");
const loading = ref(false);
const registerLoading = ref(false);
const resetLoading = ref(false);
const showResetPassword = ref(false);

// 表单引用
const loginFormRef = ref();
const registerFormRef = ref();
const resetFormRef = ref();

// 验证码倒计时（注册和重置密码功能保留）
const sendSmsCodeCountdown = ref(0);
const sendEmailCodeCountdown = ref(0);
const sendResetCodeCountdown = ref(0);

// ==================== 表单数据 ====================
// 登录表单 - 符合后端LoginRequestDTO
const loginForm = reactive({
  username: "",      // 用户名（手机号或邮箱）
  password: "",      // 密码
  rememberMe: false  // 记住我
});

// 注册表单
const registerForm = reactive({
  username: "",
  password: "",
  confirmPassword: "",
  mobile: "",
  email: "",
  realName: "",
  smsCode: "",
  emailCode: ""
});

// 重置密码表单
const resetPasswordForm = reactive({
  mobile: "",
  smsCode: "",
  newPassword: ""
});

// ==================== 表单验证规则 ====================
// 登录表单验证 - 符合后端LoginRequestDTO
const loginRules = reactive({
  username: [
    { required: true, message: "用户名不能为空", trigger: "blur" },
    { min: 2, max: 50, message: "用户名长度在 2 到 50 个字符", trigger: "blur" }
  ],
  password: [
    { required: true, message: "密码不能为空", trigger: "blur" },
    { min: 1, max: 100, message: "密码长度不能超过100个字符", trigger: "blur" }
  ]
});

// 注册表单验证
const registerRules = reactive({
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    { pattern: /^[a-zA-Z0-9_]{4,20}$/, message: "用户名为4-20位字母数字下划线", trigger: "blur" }
  ],
  mobile: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号", trigger: "blur" }
  ],
  email: [
    { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" }
  ],
  realName: [
    { max: 50, message: "真实姓名最多50个字符", trigger: "blur" }
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,32}$/, message: "密码为8-32位包含字母数字", trigger: "blur" }
  ],
  confirmPassword: [
    { required: true, message: "请确认密码", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value !== registerForm.password) {
          callback(new Error("两次输入密码不一致"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  smsCode: [
    { required: true, message: "请输入短信验证码", trigger: "blur" },
    { pattern: /^\d{6}$/, message: "验证码为6位数字", trigger: "blur" }
  ],
  emailCode: [
    { pattern: /^\d{6}$/, message: "验证码为6位数字", trigger: "blur" }
  ]
});

// 重置密码表单验证
const resetRules = reactive({
  mobile: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号", trigger: "blur" }
  ],
  smsCode: [
    { required: true, message: "请输入短信验证码", trigger: "blur" },
    { pattern: /^\d{6}$/, message: "验证码为6位数字", trigger: "blur" }
  ],
  newPassword: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    { pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,32}$/, message: "密码为8-32位包含字母数字", trigger: "blur" }
  ]
});

// ==================== 计算属性 ====================
// 验证码按钮文本（注册和重置密码功能保留）
const sendSmsCodeText = computed(() => {
  return sendSmsCodeCountdown.value > 0 ? `${sendSmsCodeCountdown.value}s` : "发送验证码";
});

const sendEmailCodeText = computed(() => {
  return sendEmailCodeCountdown.value > 0 ? `${sendEmailCodeCountdown.value}s` : "发送验证码";
});

const sendResetCodeText = computed(() => {
  return sendResetCodeCountdown.value > 0 ? `${sendResetCodeCountdown.value}s` : "发送验证码";
});

// 验证码按钮禁用状态
const sendSmsCodeDisabled = computed(() => sendSmsCodeCountdown.value > 0 || !registerForm.mobile);
const sendEmailCodeDisabled = computed(() => sendEmailCodeCountdown.value > 0 || !registerForm.email);
const sendResetCodeDisabled = computed(() => sendResetCodeCountdown.value > 0 || !resetPasswordForm.mobile);

// ==================== 核心方法 ====================

/**
 * @description 处理登录
 */
const handleLogin = async () => {
  if (!loginFormRef.value) return;

  const valid = await loginFormRef.value.validate().catch(() => false);
  if (!valid) return;

  loading.value = true;

  try {
    // 调用用户Store的登录方法
    await userStore.login(loginForm);

    // 确保Token已经持久化
    await new Promise(resolve => setTimeout(resolve, 100));

    // 添加动态路由
    await initDynamicRouter();

    // 清空 tabs、keepAlive 数据
    await tabsStore.setTabs([]);
    await keepAliveStore.setKeepAliveName([]);

    // 显示欢迎信息
    ElNotification({
      title: getTimeState(),
      message: `欢迎登录 ${userStore.realName || userStore.username}`,
      type: "success",
      duration: 3000
    });

    // 跳转到首页
    await router.isReady();
    await router.push(HOME_URL);

  } catch (error) {
    console.error("登录失败:", error);
  } finally {
    loading.value = false;
  }
};

/**
 * @description 处理注册
 */
const handleRegister = async () => {
  if (!registerFormRef.value) return;

  const valid = await registerFormRef.value.validate().catch(() => false);
  if (!valid) return;

  registerLoading.value = true;

  try {
    // 调用用户Store的注册方法
    await userStore.register(registerForm);

    // 注册成功后自动跳转到首页
    await initDynamicRouter();
    await tabsStore.setTabs([]);
    await keepAliveStore.setKeepAliveName([]);

    ElNotification({
      title: "注册成功",
      message: `欢迎加入 ${userStore.realName || userStore.username}`,
      type: "success",
      duration: 3000
    });

    await router.isReady();
    await router.push(HOME_URL);

  } catch (error) {
    console.error("注册失败:", error);
  } finally {
    registerLoading.value = false;
  }
};

/**
 * @description 处理重置密码
 */
const handleResetPassword = async () => {
  if (!resetFormRef.value) return;

  const valid = await resetFormRef.value.validate().catch(() => false);
  if (!valid) return;

  resetLoading.value = true;

  try {
    await userStore.resetPassword(resetPasswordForm);
    showResetPassword.value = false;

    // 重置表单
    resetFormRef.value.resetFields();

    // 切换到登录页面
    activeTab.value = "login";

  } catch (error) {
    console.error("重置密码失败:", error);
  } finally {
    resetLoading.value = false;
  }
};

/**
 * @description 发送验证码
 * @param {string} scene 业务场景 (login/register/reset_password)
 * @param {string} type 验证码类型 (sms/email)
 */
const sendVerifyCode = async (scene, type = 'sms') => {
  let params = { scene, type };
  let countdownRef;

  // 根据场景和类型设置参数和倒计时引用
  if (scene === 'login') {
    // 登录场景暂时不支持验证码
    ElMessage.info("登录暂不需要验证码");
    return;
  } else if (scene === 'register') {
    if (type === 'sms') {
      if (!registerForm.mobile) {
        ElMessage.warning("请先输入手机号");
        return;
      }
      params.mobile = registerForm.mobile;
      countdownRef = sendSmsCodeCountdown;
    } else if (type === 'email') {
      if (!registerForm.email) {
        ElMessage.warning("请先输入邮箱");
        return;
      }
      params.email = registerForm.email;
      countdownRef = sendEmailCodeCountdown;
    }
  } else if (scene === 'reset_password') {
    if (!resetPasswordForm.mobile) {
      ElMessage.warning("请先输入手机号");
      return;
    }
    params.mobile = resetPasswordForm.mobile;
    countdownRef = sendResetCodeCountdown;
  }

  try {
    await userStore.sendVerifyCode(params);

    // 开始倒计时
    countdownRef.value = 60;
    const timer = setInterval(() => {
      countdownRef.value--;
      if (countdownRef.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);

  } catch (error) {
    console.error("发送验证码失败:", error);
  }
};

/**
 * @description 切换标签页
 */
const handleTabChange = (tabName) => {
  // 清空表单验证状态
  if (tabName === 'login' && loginFormRef.value) {
    loginFormRef.value.clearValidate();
  } else if (tabName === 'register' && registerFormRef.value) {
    registerFormRef.value.clearValidate();
  }
};

/**
 * @description 重置表单 (兼容旧版本)
 */
const resetForm = (formEl) => {
  if (!formEl) return;
  formEl.resetFields();
};

// ==================== 生命周期 ====================

onMounted(() => {
  // 监听 enter 事件（调用登录）
  document.onkeydown = e => {
    if (e.code === "Enter" || e.code === "enter" || e.code === "NumpadEnter") {
      if (loading.value || registerLoading.value || resetLoading.value) return;

      // 根据当前标签页调用对应的方法
      if (activeTab.value === "login") {
        handleLogin();
      } else if (activeTab.value === "register") {
        handleRegister();
      }
    }
  };
});

onBeforeUnmount(() => {
  document.onkeydown = null;
});
</script>

<style scoped lang="scss">
.login-form-container {
  width: 100%;

  .login-tabs {
    :deep(.el-tabs__header) {
      margin: 0 0 30px 0;
    }

    :deep(.el-tabs__nav-wrap::after) {
      display: none;
    }

    :deep(.el-tabs__item) {
      font-size: 16px;
      font-weight: 500;
      color: #999;

      &.is-active {
        color: var(--el-color-primary);
      }
    }
  }

  .verify-code-container {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin: 0;
  }

  .login-btn {
    width: 100%;
    margin-top: 20px;
  }

  :deep(.el-form-item) {
    margin-bottom: 20px;

    .el-form-item__content {
      line-height: normal;
    }
  }

  :deep(.el-input) {
    .el-input__wrapper {
      padding: 12px 15px;
      border-radius: 8px;
      box-shadow: 0 0 0 1px var(--el-border-color) inset;
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 0 0 1px var(--el-color-primary) inset;
      }

      &.is-focus {
        box-shadow: 0 0 0 1px var(--el-color-primary) inset;
      }
    }

    .el-input__inner {
      font-size: 14px;

      &::placeholder {
        color: #c0c4cc;
      }
    }
  }

  :deep(.el-button) {
    height: 48px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;

    &.el-button--primary {
      background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
      border: none;

      &:hover {
        background: linear-gradient(135deg, var(--el-color-primary-dark-2) 0%, var(--el-color-primary) 100%);
      }
    }
  }

  :deep(.el-checkbox) {
    .el-checkbox__label {
      font-size: 14px;
      color: #666;
    }
  }

  :deep(.el-link) {
    font-size: 14px;
  }
}

// 对话框样式
:deep(.el-dialog) {
  border-radius: 12px;

  .el-dialog__header {
    padding: 20px 20px 10px;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }
  }

  .el-dialog__body {
    padding: 10px 20px 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
  }
}
</style>

<style scoped lang="scss">
@import "../index";
</style>
