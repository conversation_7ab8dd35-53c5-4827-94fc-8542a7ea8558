import { defineStore } from "pinia";
import { ElMessage } from "element-plus";
import piniaPersistConfig from "@/stores/helper/persist";
import {
  loginApi,
  registerApi,
  logoutApi,
  getCurrentUserApi,
  changePasswordApi,
  resetPasswordApi,
  sendCode<PERSON>pi
} from "@/api/modules/login";

/**
 * @description 用户信息Store - 符合JNL-Shop API规范
 */
export const useUserStore = defineStore({
  id: "wujin-user",
  state: () => ({
    // JWT Token
    token: "",
    // Token类型
    tokenType: "Bearer",
    // Token过期时间
    expiresAt: "",
    // 用户信息
    userInfo: {
      userId: null,
      username: "",
      realName: "",
      mobile: "",
      email: "",
      permissionLevel: "",
      permissionName: "",
      avatar: null,
      lastLoginTime: ""
    },
    // 登录状态
    isLoggedIn: false,
    // 记住我状态
    rememberMe: false
  }),

  getters: {
    // 获取用户ID
    userId: (state) => state.userInfo.userId,
    // 获取用户名
    username: (state) => state.userInfo.username,
    // 获取真实姓名
    realName: (state) => state.userInfo.realName,
    // 获取权限等级
    permissionLevel: (state) => state.userInfo.permissionLevel,
    // 获取权限名称
    permissionName: (state) => state.userInfo.permissionName,
    // 获取完整Token
    fullToken: (state) => state.token ? `${state.tokenType} ${state.token}` : "",
    // 检查Token是否过期
    isTokenExpired: (state) => {
      if (!state.expiresAt) return false;
      return new Date().getTime() > new Date(state.expiresAt).getTime();
    }
  },

  actions: {
    // ==================== 基础方法 ====================

    /**
     * @description 设置Token
     */
    setToken(token) {
      this.token = token;
      this.isLoggedIn = !!token;
    },

    /**
     * @description 设置用户信息
     */
    setUserInfo(userInfo) {
      this.userInfo = { ...this.userInfo, ...userInfo };
    },

    /**
     * @description 设置Token过期时间
     */
    setExpiresAt(expiresAt) {
      this.expiresAt = expiresAt;
    },

    /**
     * @description 设置记住我状态
     */
    setRememberMe(rememberMe) {
      this.rememberMe = rememberMe;
    },

    /**
     * @description 清除用户信息
     */
    clearUserInfo() {
      this.token = "";
      this.tokenType = "Bearer";
      this.expiresAt = "";
      this.userInfo = {
        userId: null,
        username: "",
        realName: "",
        mobile: "",
        email: "",
        permissionLevel: "",
        permissionName: "",
        avatar: null,
        lastLoginTime: ""
      };
      this.isLoggedIn = false;
      this.rememberMe = false;
    },

    // ==================== 业务方法 ====================

    /**
     * @description 用户登录
     * @param {Object} loginForm 登录表单数据
     * @returns {Promise} 登录结果
     */
    async login(loginForm) {
      try {
        const { data } = await loginApi(loginForm);
        this.setToken(data.access_token);

        ElMessage.success("登录成功");
        return data;
      } catch (error) {
        ElMessage.error(error.message || "登录失败");
        throw error;
      }
    },

    /**
     * @description 用户注册
     * @param {Object} registerForm 注册表单数据
     * @returns {Promise} 注册结果
     */
    async register(registerForm) {
      try {
        const response = await registerApi(registerForm);
        const { accessToken, tokenType, expiresAt, userInfo } = response.data;

        // 注册成功后自动登录
        this.setToken(accessToken);
        this.tokenType = tokenType || "Bearer";
        this.setExpiresAt(expiresAt);
        this.setUserInfo(userInfo);

        ElMessage.success("注册成功");
        return response;
      } catch (error) {
        ElMessage.error(error.message || "注册失败");
        throw error;
      }
    },

    /**
     * @description 用户登出
     * @returns {Promise} 登出结果
     */
    async logout() {
      try {
        await logoutApi();
        this.clearUserInfo();
        ElMessage.success("登出成功");
      } catch (error) {
        // 即使接口调用失败，也要清除本地信息
        this.clearUserInfo();
        console.error("登出接口调用失败:", error);
      }
    },

    /**
     * @description 获取当前用户信息
     * @returns {Promise} 用户信息
     */
    async getCurrentUser() {
      try {
        const response = await getCurrentUserApi();
        this.setUserInfo(response.data);
        return response;
      } catch (error) {
        ElMessage.error("获取用户信息失败");
        throw error;
      }
    },

    /**
     * @description 修改密码
     * @param {Object} passwordForm 密码表单数据
     * @returns {Promise} 修改结果
     */
    async changePassword(passwordForm) {
      try {
        const response = await changePasswordApi(passwordForm);
        ElMessage.success("密码修改成功，请重新登录");
        // 修改密码后需要重新登录
        this.clearUserInfo();
        return response;
      } catch (error) {
        ElMessage.error(error.message || "密码修改失败");
        throw error;
      }
    },

    /**
     * @description 重置密码
     * @param {Object} resetForm 重置表单数据
     * @returns {Promise} 重置结果
     */
    async resetPassword(resetForm) {
      try {
        const response = await resetPasswordApi(resetForm);
        ElMessage.success("密码重置成功，请使用新密码登录");
        return response;
      } catch (error) {
        ElMessage.error(error.message || "密码重置失败");
        throw error;
      }
    },

    /**
     * @description 发送验证码
     * @param {Object} codeForm 验证码表单数据
     * @returns {Promise} 发送结果
     */
    async sendVerifyCode(codeForm) {
      try {
        const response = await sendCodeApi(codeForm);
        ElMessage.success("验证码发送成功");
        return response;
      } catch (error) {
        ElMessage.error(error.message || "验证码发送失败");
        throw error;
      }
    }
  },

  persist: piniaPersistConfig("wujin-user")
});
