<template>
  <div class="table-box">

    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :request-auto="true"
      :init-param="initParam"
      :data-callback="dataCallback"
      :pagination="true"
      :page-size="20"
      :show-overflow-tooltip="true"
      @drag-sort="sortTable"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button v-auth="'add'" type="primary" :icon="CirclePlus" @click="openDrawer('新增')">新增商品</el-button>
        <el-button v-auth="'batchAdd'" type="primary" :icon="Upload" plain @click="batchAdd">批量添加商品</el-button>
        <el-button v-auth="'export'" type="primary" :icon="Download" plain @click="downloadFile">导出商品数据</el-button>
        <el-button type="danger" :icon="Delete" plain :disabled="!scope.isSelected" @click="batchDelete(scope.selectedListIds)">
          批量删除商品
        </el-button>
      </template>
      <!-- Expand -->
      <template #expand="scope">
        <div class="expand-content">
          <div class="expand-row">
            <span class="label">商品描述：</span>
            <span>{{ scope.row.describe || '暂无描述' }}</span>
          </div>
          <div class="expand-row">
            <span class="label">创建时间：</span>
            <span>{{ scope.row.createTime }}</span>
          </div>
          <div class="expand-row">
            <span class="label">修改时间：</span>
            <span>{{ scope.row.updateTime }}</span>
          </div>
        </div>
      </template>
      <!-- 商品状态渲染 -->
      <template #status="scope">
        <el-tag :type="getStatusType(scope.row.status)">
          {{ getStatusText(scope.row.status) }}
        </el-tag>
      </template>
      <!-- 图片渲染 -->
      <!-- <template #sku="scope">
        <LazyImage
          :src="scope.row.sku"
          :alt="scope.row.productName || '商品图片'"
          width="50px"
          height="50px"
          :preview="true"
          :lazy="true"
        />
      </template> -->
      <!-- 价格渲染 -->
      <template #price="scope">
        <span class="price-text">¥{{ scope.row.price?.toFixed(2) || '0.00' }}</span>
      </template>
      <!-- 库存渲染 -->
      <template #inventory="scope">
        <el-tag :type="scope.row.inventory > 10 ? 'success' : scope.row.inventory > 0 ? 'warning' : 'danger'">
          {{ scope.row.inventory || 0 }}
        </el-tag>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <el-button type="primary" link :icon="View" @click="openDrawer('查看', scope)">查看</el-button>
        <el-button type="primary" link :icon="EditPen" @click="openDrawer('编辑', scope)">编辑</el-button>
        <el-button type="primary" link :icon="Delete" @click="deleteProduct(scope.row)">删除</el-button>
      </template>
    </ProTable>
    <ProductDrawer ref="drawerRef" />
    <ImportExcel ref="dialogRef" />
  </div>
</template>

<script setup lang="jsx" name="useProTable">
import { ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { useHandleData } from "@/hooks/useHandleData";
import { useDownload } from "@/hooks/useDownload";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { ElMessage, ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import ImportExcel from "@/components/ImportExcel/index.vue";
import ProductDrawer from "@/views/proTable/components/ProductDrawer.vue";
import LazyImage from "@/components/LazyImage/index.vue";
import { CirclePlus, Delete, EditPen, Download, Upload, View, Refresh } from "@element-plus/icons-vue";
import {
  getProductList,
  getProductByCode,
  getProductByBrand,
  getProductByName,
  getProductByCategory,
  deleteProduct as deleteProductApi,
  addProduct,
  editProduct,
  exportProductInfo,
  batchAddProduct,
  getProductStatus,
  getProductCategories
} from "@/api/modules/product";
import dayjs from "dayjs";

const router = useRouter();

const props = defineProps({
  searchParam: {
    type: Object,
    default: () => ({})
  }
});

// ProTable 实例
const proTable = ref();

// 初始化请求参数
const initParam = reactive({});

// dataCallback 是对于返回的表格数据做处理
const dataCallback = data => {
  // console.log('商品数据回调:', data);
  // 处理后端返回的数据格式 {code: 200, message: "操作成功", data: {total: 267002, records: [...]}}
  if (data && data.data) {
    const records = data.data.records || [];
    return {
      list: records,
      total: data.data.total || 0
    };
  }
  // 兼容其他可能的数据格式
  return {
    list: data.records || data.list || [],
    total: data.total || 0
  };
};

// 获取商品列表
const getTableList = async params => {
  // console.log('请求商品列表参数:', params);
  try {
    // 处理搜索参数
    let newParams = JSON.parse(JSON.stringify(params));
    console.log('处理后的搜索参数:', newParams);
    

    // 根据不同的搜索条件调用不同的API
    if (newParams.productCode) {
      return await getProductByCode(newParams);
    } else if (newParams.barCode) {
      return await getProductByBrand(newParams);
    } else if (newParams.productName) {
      return await getProductByName(newParams);
    } else if (newParams.categoryId) {
      return await getProductByCategory(newParams.categoryId, newParams);
    } else {
      return await getProductList(newParams);
    }
  } catch (error) {
    console.error('获取商品列表失败:', error);
    throw error;
  }
};

// 页面按钮权限
const { BUTTONS } = useAuthButtons();

// 获取商品状态类型
const getStatusType = (status) => {
  switch (status) {
    case 0: return 'success'; // 在售
    case 1: return 'danger';  // 下架
    case 2: return 'warning'; // 缺货
    default: return 'info';
  }
};

// 获取商品状态文本
const getStatusText = (status) => {
  switch (status) {
    case 0: return '在售';
    case 1: return '下架';
    case 2: return '缺货';
    default: return '未知';
  }
};

// 表格配置项
const columns = reactive([
  { type: "selection", fixed: "left", width: 70 },
  { type: "expand", label: "详情", width: 85 },
  {
    prop: "pid",
    label: "商品ID",
    width: 100
  },
  {
    prop: "barCode",
    label: "条码",
    width: 150,
    search: { el: "input", tooltip: "请输入商品条码", key: "barCode" }
  },
  {
    prop: "productCode",
    label: "商品编码",
    width: 150,
    search: { el: "input", tooltip: "请输入商品编码", key: "productCode" }
  },
  {
    prop: "productName",
    label: "商品名称",
    width: 200,
    search: { el: "input", tooltip: "请输入商品名称", key: "productName" },
    render: scope => {
      return (
        <el-button type="primary" link onClick={() => openDrawer('查看', scope)}>
          {scope.row.productName}
        </el-button>
      );
    }
  },
  {
    prop: "brand",
    label: "品牌",
    width: 120,
    search: { el: "input", tooltip: "请输入品牌名称", key: "brand" }
  },
  {
    prop: "typeName",
    label: "分类",
    width: 150,
    enum: getProductCategories,
    search: { el: "input", tooltip: "请输入分类", key: "typeName" },

    // fieldNames: { label: "fname", value: "fid" },
    render: scope => {
      return (
        <el-button type="primary" link onClick={() => openDrawer('查看', scope)}>
          {scope.row.typeName}
        </el-button>
      );
    }
  },
  // {
  //   prop: "sku",
  //   label: "SKU",
  //   width: 100
  // },
  {
    prop: "status",
    label: "商品状态",
    width: 100,
    enum: getProductStatus,
    // search: { el: "select", props: { filterable: true }, key: "status" },
    fieldNames: { label: "label", value: "value" }
  },
  {
    prop: "price",
    label: "标准售价",
    width: 120,
    // search: {
    //   render: ({ searchParam }) => {
    //     return (
    //       <div class="flx-center">
    //         <el-input-number vModel={searchParam.minPrice} placeholder="最低价格" precision={2} />
    //         <span class="mr10 ml10">-</span>
    //         <el-input-number vModel={searchParam.maxPrice} placeholder="最高价格" precision={2} />
    //       </div>
    //     );
    //   }
    // }
  },
  {
    prop: "inventory",
    label: "库存",
    width: 100,
    // search: {
    //   render: ({ searchParam }) => {
    //     return (
    //       <div class="flx-center">
    //         <el-input-number vModel={searchParam.minInventory} placeholder="最小库存" />
    //         <span class="mr10 ml10">-</span>
    //         <el-input-number vModel={searchParam.maxInventory} placeholder="最大库存" />
    //       </div>
    //     );
    //   }
    // }
  },
  {
    prop: "createTime",
    label: "创建时间",
    width: 180,
    // search: {
    //   el: "date-picker",
    //   span: 2,
    //   props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" }
    // },
    render: scope => {
      return <>{scope.row.createTime}</>;
    }
  },
  { prop: "operation", label: "操作", fixed: "right", width: 250 }
]);

// 表格拖拽排序
const sortTable = ({ newIndex, oldIndex }) => {
  ElMessage.success("修改商品排序成功");
};

// 删除商品信息
const deleteProduct = async params => {
  await useHandleData(deleteProductApi, params.pid, `删除商品【${params.productName}】`);
  proTable.value?.getTableList();
};

// 批量删除商品信息
const batchDelete = async ids => {
  await useHandleData(deleteProductApi, { ids }, "删除所选商品信息");
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

// 导出商品列表
const downloadFile = async () => {
  ElMessageBox.confirm("确认导出商品数据?", "温馨提示", { type: "warning" }).then(async () => {
    try {
      // 根据API文档，导出数据不需要template参数，或者template=false
      const exportParams = {
        ...proTable.value?.searchParam,
        template: false
      };
      console.log('导出商品数据参数:', exportParams);

      // 先测试API是否可访问
      console.log('开始导出商品数据...');
      await useDownload(exportProductInfo, "商品列表", exportParams);
      console.log('导出完成');
    } catch (error) {
      console.error('导出失败:', error);
      ElMessage.error('导出失败: ' + (error.message || '未知错误'));
    }
  });
};

// 批量添加商品
const dialogRef = ref(null);
const batchAdd = () => {
  const params = {
    title: "商品",
    tempApi: exportProductInfo,
    importApi: batchAddProduct,
    getTableList: proTable.value?.getTableList
  };
  dialogRef.value?.acceptParams(params);
};

// 打开 drawer(新增、查看、编辑)
const drawerRef = ref(null);
const openDrawer = (title, row) => {
  const params = {
    title,
    isView: title === "查看",
    row: { ...row?.row } || {},
    api: title === "新增" ? addProduct : title === "编辑" ? editProduct : undefined,
    getTableList: proTable.value?.getTableList
  };
  drawerRef.value?.acceptParams(params);
};
</script>

<style scoped lang="scss">
.expand-content {
  padding: 10px;

  .expand-row {
    display: flex;
    margin-bottom: 8px;

    .label {
      font-weight: bold;
      width: 100px;
      color: #606266;
    }
  }
}

.price-text {
  font-weight: bold;
  color: #e6a23c;
  font-size: 14px;
}

/* 图片预览样式修复 */
:deep(.el-image-viewer__wrapper) {
  z-index: 9999 !important;
}

:deep(.el-image-viewer__mask) {
  z-index: 9998 !important;
}

/* 确保图片预览在最顶层 */
:deep(.el-overlay) {
  z-index: 9999 !important;
}

/* 图片预览容器样式 */
.el-image {
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
}

.el-image:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>
