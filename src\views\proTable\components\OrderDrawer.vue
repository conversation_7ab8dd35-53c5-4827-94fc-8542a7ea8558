<template>
  <el-drawer v-model="drawerVisible" :destroy-on-close="true" size="600px" :title="`${drawerProps.title}订单`">
    <el-form
      ref="ruleFormRef"
      label-width="120px"
      label-suffix=" :"
      :rules="rules"
      :disabled="drawerProps.isView"
      :model="drawerProps.row"
      :hide-required-asterisk="drawerProps.isView"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="订单ID" prop="oid">
            <el-input v-model="drawerProps.row.oid" placeholder="系统自动生成" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户ID" prop="uid">
            <el-input v-model="drawerProps.row.uid" placeholder="请填写用户ID" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="收件人" prop="addressee">
            <el-input v-model="drawerProps.row.addressee" placeholder="请填写收件人姓名" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="ophone">
            <el-input v-model="drawerProps.row.ophone" placeholder="请填写联系电话" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="收货地址" prop="olocation">
        <el-input v-model="drawerProps.row.olocation" placeholder="请填写收货地址" clearable></el-input>
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="订单金额" prop="oprice">
            <el-input-number 
              v-model="drawerProps.row.oprice" 
              :precision="2" 
              :min="0" 
              placeholder="请填写订单金额"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="订单状态" prop="ostatus">
            <!-- 查看模式：显示状态标签 -->
            <el-input
              v-if="drawerProps.isView"
              :value="getStatusLabel(drawerProps.row.ostatus)"
              disabled
              style="width: 100%"
            />
            <!-- 编辑模式：下拉选择 -->
            <el-select
              v-else
              v-model="drawerProps.row.ostatus"
              placeholder="请选择订单状态"
              clearable
              style="width: 100%"
            >
              <el-option v-for="item in orderStatus" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="支付平台" prop="payPal">
            <el-select v-model="drawerProps.row.payPal" placeholder="请选择支付平台" clearable style="width: 100%">
              <el-option v-for="item in payPlatform" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="支付单号" prop="payid">
            <el-input v-model="drawerProps.row.payid" placeholder="请填写支付单号" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="创建时间" prop="createTime">
            <el-input v-model="drawerProps.row.createTime" placeholder="系统自动生成" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="支付时间" prop="payTime">
            <el-input v-model="drawerProps.row.payTime" placeholder="支付完成后自动生成" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="微信订单号" prop="transactionId">
        <el-input v-model="drawerProps.row.transactionId" placeholder="微信支付订单号" clearable></el-input>
      </el-form-item>
      
      <el-form-item label="退款单号" prop="refundNo">
        <el-input v-model="drawerProps.row.refundNo" placeholder="退款单号" clearable></el-input>
      </el-form-item>
      
      <!-- 订单商品列表 -->
      <el-form-item label="订单商品" v-if="drawerProps.isView && drawerProps.row.products">
        <el-table :data="drawerProps.row.products" border style="width: 100%">
          <el-table-column prop="pname" label="商品名称" width="150"></el-table-column>
          <el-table-column prop="pnum" label="数量" width="80"></el-table-column>
          <el-table-column prop="pprice" label="单价" width="100">
            <template #default="scope">
              ¥{{ scope.row.pprice?.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="totalPrice" label="小计" width="100">
            <template #default="scope">
              ¥{{ scope.row.totalPrice?.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="sku" label="SKU" show-overflow-tooltip></el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="drawerVisible = false">取消</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-drawer>
</template>

<script setup name="OrderDrawer">
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import { orderStatus, payPlatform } from "@/utils/dict";

// 根据状态值获取状态标签
const getStatusLabel = (statusValue) => {
  const status = orderStatus.find(item => item.value === statusValue);
  return status ? status.label : '未知状态';
};

const rules = reactive({
  addressee: [{ required: true, message: "请填写收件人姓名", trigger: "blur" }],
  ophone: [{ required: true, message: "请填写联系电话", trigger: "blur" }],
  olocation: [{ required: true, message: "请填写收货地址", trigger: "blur" }],
  oprice: [{ required: true, message: "请填写订单金额", trigger: "blur" }],
  ostatus: [{ required: true, message: "请选择订单状态", trigger: "change" }]
});

// drawer框状态
const drawerVisible = ref(false);
const drawerProps = ref({
  isView: false,
  title: "",
  row: {}
});

// 接收父组件传过来的参数
const acceptParams = params => {
  drawerProps.value = params;
  drawerVisible.value = true;
};

// 提交数据（新增/编辑）
const ruleFormRef = ref();
const handleSubmit = () => {
  ruleFormRef.value.validate(async valid => {
    if (!valid) return;
    try {
      await drawerProps.value.api(drawerProps.value.row);
      ElMessage.success({ message: `${drawerProps.value.title}订单成功！` });
      drawerProps.value.getTableList();
      drawerVisible.value = false;
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
