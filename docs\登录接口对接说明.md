# 登录接口对接说明

## 📋 后端接口规范

### LoginRequestDTO 参数说明

根据后端提供的 `LoginRequestDTO`，登录接口需要接收以下参数：

```java
@Data
@Schema(description = "登录请求")
public class LoginRequestDTO implements Serializable {
    
    @Schema(description = "用户名（手机号或邮箱）", example = "13800138000")
    @NotBlank(message = "用户名不能为空")
    private String username;

    @Schema(description = "密码", example = "password123")
    @NotBlank(message = "密码不能为空")
    private String password;

    @Schema(description = "记住我", example = "false")
    private Boolean rememberMe = false;
}
```

### 接口地址
- **POST** `/jnl_/auth/login`

### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| username | String | 是 | 用户名（手机号或邮箱） | "13800138000" |
| password | String | 是 | 密码 | "password123" |
| rememberMe | Boolean | 否 | 记住我，默认false | false |

### 请求示例
```json
{
  "username": "13800138000",
  "password": "password123",
  "rememberMe": false
}
```

## 🔧 前端实现

### 1. API接口定义

```javascript
// src/api/modules/login.js
export const loginApi = (params) => {
  // 确保参数符合后端DTO要求
  const loginRequest = {
    username: params.username,
    password: params.password,
    rememberMe: params.rememberMe || false
  };
  
  return http.post(PORT1 + "/auth/login", loginRequest, { loading: true });
};
```

### 2. 登录表单数据结构

```javascript
// 登录表单 - 符合后端LoginRequestDTO
const loginForm = reactive({
  username: "",      // 用户名（手机号或邮箱）
  password: "",      // 密码
  rememberMe: false  // 记住我
});
```

### 3. 表单验证规则

```javascript
const loginRules = reactive({
  username: [
    { required: true, message: "用户名不能为空", trigger: "blur" },
    { min: 2, max: 50, message: "用户名长度在 2 到 50 个字符", trigger: "blur" }
  ],
  password: [
    { required: true, message: "密码不能为空", trigger: "blur" },
    { min: 1, max: 100, message: "密码长度不能超过100个字符", trigger: "blur" }
  ]
});
```

### 4. 用户Store登录方法

```javascript
async login(loginForm) {
  try {
    const response = await loginApi(loginForm);
    const { data } = response;
    
    if (data.token) {
      this.setToken(data.token);
      this.tokenType = "Bearer";
      
      if (data.userInfo) {
        this.setUserInfo(data.userInfo);
      }
      
      this.setRememberMe(loginForm.rememberMe || false);
    }
    
    ElMessage.success(response.message || "登录成功");
    return response;
  } catch (error) {
    const errorMessage = error.message || error.msg || "登录失败";
    ElMessage.error(errorMessage);
    throw error;
  }
}
```

## 📝 后端响应格式

### 成功响应示例
```json
{
  "code": 200,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userInfo": {
      "userId": 1,
      "username": "13800138000",
      "realName": "张三",
      "mobile": "13800138000",
      "email": "<EMAIL>",
      "permissionLevel": "admin"
    }
  },
  "message": "登录成功"
}
```

### 失败响应示例
```json
{
  "code": 400,
  "data": null,
  "message": "用户名或密码错误"
}
```

## 🔒 安全特性

### 1. 请求头自动添加
```javascript
headers: {
  'Authorization': 'Bearer {token}',
  'X-Request-ID': 'req_timestamp_randomstring',
  'X-Timestamp': '1640995200000',
  'X-Client-Version': '1.0.0',
  'Content-Type': 'application/json'
}
```

### 2. 错误处理
- 显示后端返回的实际错误信息
- 自动处理401未授权错误
- 支持Token自动刷新机制

### 3. 数据持久化
- 使用Pinia进行状态管理
- 支持本地存储持久化
- 记住我功能支持

## 🧪 测试方法

### 1. 浏览器控制台测试
```javascript
// 在浏览器控制台中执行
const testLogin = async () => {
  try {
    const loginData = {
      username: "<EMAIL>",
      password: "password123",
      rememberMe: false
    };
    const res = await loginApi(loginData);
    console.log('登录成功:', res);
  } catch (error) {
    console.error('登录失败:', error);
  }
};

testLogin();
```

### 2. 登录页面测试
1. 访问 `http://localhost:80/#/login`
2. 输入用户名和密码
3. 点击登录按钮
4. 查看控制台输出和网络请求

## 📋 注意事项

1. **参数验证**：前端已移除验证码相关字段，仅保留必要的三个字段
2. **错误处理**：显示后端返回的实际错误信息，而不是通用错误
3. **Token管理**：自动添加Bearer Token到请求头
4. **兼容性**：支持多种响应格式，兼容不同的后端实现
5. **安全性**：包含请求追踪、时间戳等企业级安全特性

## 🔄 后续扩展

如果后端需要支持验证码功能，可以取消注释相关代码：
- 登录表单中的验证码字段
- 验证码发送功能
- 相关的表单验证规则

当前实现完全符合提供的 `LoginRequestDTO` 规范，可以直接对接后端接口。
