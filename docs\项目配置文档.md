# 五金商城后台管理系统 - 项目配置文档

## 📋 目录
- [权限系统配置](#权限系统配置)
- [路由配置](#路由配置)
- [API环境配置](#api环境配置)
- [环境变量配置](#环境变量配置)
- [关键配置文件位置](#关键配置文件位置)

---

## 🔐 权限系统配置

### 权限等级
项目采用**固定权限等级**系统，共有3个权限等级：

| 权限等级 | 代码 | 描述 | 权限范围 |
|---------|------|------|----------|
| 🔑 **Root权限** | `root` | 超级管理员 | 拥有所有权限 |
| 👨‍💼 **Admin权限** | `admin` | 管理员 | 无删除和上传权限 |
| 👤 **Operator权限** | `operator` | 操作员 | 只能查看和导出 |

### 详细权限配置

#### Root权限 (root)
```javascript
permissions: ['add', 'edit', 'delete', 'import', 'export', 'batchAdd', 'batchDelete', 'status', 'view', 'upload']
```

#### Admin权限 (admin)
```javascript
permissions: ['add', 'edit', 'import', 'export', 'batchAdd', 'status', 'view']
```

#### Operator权限 (operator)
```javascript
permissions: ['view', 'export']
```

### 权限配置文件位置
- **权限配置**: `src/config/permissions.js`
- **权限指令**: `src/directives/modules/auth.js`
- **权限Store**: `src/stores/modules/auth.js`
- **权限Hook**: `src/hooks/useAuthButtons.js`
- **本地测试数据**: `src/assets/json/permissionLevel.json`

### 使用方式

#### 1. v-auth 指令
```vue
<!-- 单个权限 -->
<el-button v-auth="'add'" type="primary">新增</el-button>
<el-button v-auth="'delete'" type="danger">删除</el-button>

<!-- 多个权限（需要同时拥有） -->
<el-button v-auth="['add', 'edit']" type="primary">批量操作</el-button>
```

#### 2. useAuthButtons Hook
```javascript
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS, permissionLevel, permissions } = useAuthButtons();

// 在模板中使用
<el-button v-if="BUTTONS.add" type="primary">新增</el-button>
```

#### 3. 手动权限检查
```javascript
import { hasPermission } from "@/config/permissions";
import { useAuthStore } from "@/stores/modules/auth";

const authStore = useAuthStore();
const canDelete = hasPermission(authStore.permissionLevelGet, 'delete');
```

---

## 🛣️ 路由配置

### 路由文件结构
```
src/routers/
├── index.js                    # 路由主文件，包含路由守卫
├── modules/
│   ├── staticRouter.js         # 静态路由配置
│   └── dynamicRouter.js        # 动态路由初始化
```

### 静态路由
- **登录页**: `/login`
- **首页**: `/home/<USER>
- **错误页**: `/403`, `/404`, `/500`

### 动态路由
- 基于菜单权限动态生成
- 菜单数据来源: `src/assets/json/authMenuList.json`
- 自动加载 `src/views/**/*.vue` 组件

### 路由守卫流程
1. 检查是否访问登录页
2. 检查路由白名单
3. 验证Token
4. 初始化动态路由
5. 设置按钮权限

### 路由配置文件位置
- **主路由**: `src/routers/index.js`
- **静态路由**: `src/routers/modules/staticRouter.js`
- **动态路由**: `src/routers/modules/dynamicRouter.js`
- **菜单数据**: `src/assets/json/authMenuList.json`
- **全局配置**: `src/config/index.js`

---

## 🌐 API环境配置

### 后端接口地址
- **开发环境**: `http://127.0.0.1:4396/jnl_`
- **测试环境**: `http://127.0.0.1:4396/jnl_`
- **生产环境**: `http://127.0.0.1:4396/jnl_`

### API配置文件位置
- **HTTP配置**: `src/api/index.js`
- **服务端口**: `src/api/config/servicePort.js`
- **登录接口**: `src/api/modules/login.js`
- **错误处理**: `src/utils/errorHandler.js`
- **表单验证**: `src/utils/validate.js`

### 🔐 JNL-Shop 认证授权API接口

#### 认证管理接口
```javascript
// 用户登录
POST /jnl_/auth/login
// 参数: { username, password, verifyCode?, verifyType?, rememberMe? }
// 返回: { code: 200, data: { accessToken, tokenType, expiresAt, userInfo }, message }

// 用户注册
POST /jnl_/auth/register
// 参数: { username, password, confirmPassword, mobile, email?, realName?, smsCode, emailCode? }
// 返回: { code: 200, data: { accessToken, tokenType, expiresAt, userInfo }, message }

// 发送验证码
POST /jnl_/auth/send-code
// 参数: { type: 'sms'|'email', mobile?, email?, scene: 'register'|'login'|'reset_password' }
// 返回: { code: 200, data: null, message }

// 用户登出
POST /jnl_/auth/logout
// 返回: { code: 200, data: null, message }

// 刷新Token
POST /jnl_/auth/refresh-token
// 返回: { code: 200, data: { accessToken, userInfo }, message }

// 验证Token
GET /jnl_/auth/validate-token
// 返回: { code: 200, data: { valid: true }, message }

// 获取当前用户信息
GET /jnl_/auth/current-user
// 返回: { code: 200, data: { userId, username, realName, mobile, email, permissionLevel, ... }, message }

// 修改密码
POST /jnl_/auth/change-password (application/x-www-form-urlencoded)
// 参数: { oldPassword, newPassword }
// 返回: { code: 200, data: null, message }

// 重置密码
POST /jnl_/auth/reset-password (application/x-www-form-urlencoded)
// 参数: { mobile, smsCode, newPassword }
// 返回: { code: 200, data: null, message }
```

#### 权限管理接口
```javascript
// 授权用户权限 (仅root用户可用)
POST /jnl_/permission/grant (application/x-www-form-urlencoded)
// 参数: { targetUserId, permissionLevel: 'root'|'admin'|'operator'|'guest' }
// 返回: { code: 200, data: null, message }

// 获取用户权限等级 (兼容旧版本)
GET /jnl_/auth/buttons
// 返回: { "code": 200, "data": { "permission": "root" }, "msg": "获取权限等级成功" }

// 获取菜单列表 (兼容旧版本)
GET /jnl_/menu/list
// 返回: { code: 200, data: [...], message }
```

#### 业务接口
```javascript
// 商品相关
GET /jnl_/product/page          # 商品分页
GET /jnl_/product/code          # 根据编码查询
GET /jnl_/product/brand         # 根据品牌查询
GET /jnl_/product/name          # 根据名称查询
GET /jnl_/product/category/{fid} # 根据分类查询

// 订单相关
GET /jnl_/order/*               # 订单相关接口

// 分类相关
GET /jnl_/category/*            # 分类相关接口

// 文件上传
POST /jnl_/upload/batch         # 批量上传
```

---

## ⚙️ 环境变量配置

### 环境文件
- **开发环境**: `.env.development`
- **测试环境**: `.env.test`
- **生产环境**: `.env.production`

### 关键环境变量

#### 开发环境 (.env.development)
```bash
# 基础配置
VITE_USER_NODE_ENV = development
VITE_PUBLIC_PATH = /
VITE_ROUTER_MODE = hash

# API配置
VITE_API_URL = /jnl_
VITE_PROXY = [["/jnl_","http://127.0.0.1:4396"]]

# 功能开关
VITE_DROP_CONSOLE = true
VITE_PWA = false
```

#### 生产环境 (.env.production)
```bash
# 基础配置
VITE_USER_NODE_ENV = production
VITE_PUBLIC_PATH = /
VITE_ROUTER_MODE = hash

# API配置
VITE_API_URL = "http://127.0.0.1:4396/jnl_"

# 构建配置
VITE_BUILD_COMPRESS = none
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false
VITE_DROP_CONSOLE = true
VITE_PWA = true
```

---

## 📁 关键配置文件位置

### 核心配置
| 配置类型 | 文件路径 | 说明 |
|---------|----------|------|
| **项目配置** | `vite.config.js` | Vite构建配置 |
| **全局配置** | `src/config/index.js` | 全局常量配置 |
| **环境配置** | `.env.*` | 环境变量配置 |

### 权限系统
| 配置类型 | 文件路径 | 说明 |
|---------|----------|------|
| **权限配置** | `src/config/permissions.js` | 权限等级和权限定义 |
| **权限Store** | `src/stores/modules/auth.js` | 权限状态管理 |
| **权限指令** | `src/directives/modules/auth.js` | v-auth指令 |
| **权限Hook** | `src/hooks/useAuthButtons.js` | 权限检查Hook |
| **测试数据** | `src/assets/json/permissionLevel.json` | 本地权限测试数据 |

### 路由系统
| 配置类型 | 文件路径 | 说明 |
|---------|----------|------|
| **主路由** | `src/routers/index.js` | 路由配置和守卫 |
| **静态路由** | `src/routers/modules/staticRouter.js` | 静态路由定义 |
| **动态路由** | `src/routers/modules/dynamicRouter.js` | 动态路由初始化 |
| **菜单数据** | `src/assets/json/authMenuList.json` | 菜单配置数据 |

### API系统
| 配置类型 | 文件路径 | 说明 |
|---------|----------|------|
| **HTTP配置** | `src/api/index.js` | Axios配置和拦截器 |
| **服务端口** | `src/api/config/servicePort.js` | 微服务端口配置 |
| **登录接口** | `src/api/modules/login.js` | 登录相关接口 |

### 状态管理
| 配置类型 | 文件路径 | 说明 |
|---------|----------|------|
| **权限Store** | `src/stores/modules/auth.js` | 权限状态管理 |
| **用户Store** | `src/stores/modules/user.js` | 用户信息管理 |
| **全局Store** | `src/stores/modules/global.js` | 全局设置管理 |

### 构建配置
| 配置类型 | 文件路径 | 说明 |
|---------|----------|------|
| **Vite配置** | `vite.config.js` | 主构建配置 |
| **环境处理** | `build/getEnv.js` | 环境变量处理 |
| **代理配置** | `build/proxy.js` | 开发代理配置 |
| **插件配置** | `build/plugins.js` | Vite插件配置 |

---

## 🔒 企业级安全配置

### JWT Token认证
- **Token类型**: Bearer Token
- **存储方式**: Pinia持久化存储
- **自动刷新**: 支持Token过期自动刷新
- **请求头**: `Authorization: Bearer {token}`

### 安全特性
- **请求ID追踪**: 每个请求携带唯一ID用于日志追踪
- **时间戳验证**: 防重放攻击
- **客户端版本**: 请求头携带客户端版本信息
- **错误处理**: 企业级错误分类和处理
- **密码强度**: 8-32位包含字母数字的强密码策略

### 请求拦截器特性
```javascript
// 请求头自动添加
headers: {
  'Authorization': 'Bearer {token}',
  'X-Request-ID': 'req_timestamp_randomstring',
  'X-Timestamp': '1640995200000',
  'X-Client-Version': '1.0.0'
}
```

### 响应拦截器特性
- **自动Token刷新**: 401错误时自动刷新Token
- **错误分类处理**: 根据错误码分类处理
- **用户友好提示**: 显示后端返回的实际错误信息
- **请求队列**: Token刷新期间请求排队处理

### 表单验证规范
- **用户名**: 4-20位字母数字下划线
- **密码**: 8-32位包含字母数字
- **手机号**: 中国大陆手机号格式
- **邮箱**: 标准邮箱格式
- **验证码**: 6位数字

---

## 🚀 快速开始

### 1. 修改权限等级
编辑 `src/assets/json/permissionLevel.json`:
```json
{
  "code": 200,
  "data": {
    "permission": "admin"  // 改为 root/admin/operator
  },
  "msg": "获取权限等级成功"
}
```

### 2. 修改API地址
编辑对应环境的 `.env.*` 文件:
```bash
VITE_API_URL = "http://your-api-domain.com/jnl_"
```

### 3. 添加新权限
编辑 `src/config/permissions.js`:
```javascript
export const ALL_PERMISSIONS = [
  'add', 'edit', 'delete', 'import', 'export',
  'newPermission'  // 添加新权限
];
```

### 4. 本地开发模式
在 `src/api/modules/login.js` 中切换本地/远程数据:
```javascript
// 使用本地数据
return permissionLevel;

// 使用远程API
// return http.get(PORT1 + `/auth/buttons`, {}, { loading: false });
```

---

## 📞 技术支持

如有问题，请检查以上配置文件或联系开发团队。
