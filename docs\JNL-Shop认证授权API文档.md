# JNL-Shop 认证授权 API 文档

## 概述

本文档描述了JNL-Shop系统的认证授权相关API接口，包括用户登录、注册、权限管理等功能。

### 基础信息
- **Base URL**: `http://localhost:8080`
- **Content-Type**: `application/json`
- **认证方式**: <PERSON><PERSON> (JWT)
- **权限等级**: root > admin > operator > guest

### 通用响应格式

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {},
    "timestamp": "2025-07-15T10:30:00"
}
```

### 错误码说明
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权或Token无效
- `403`: 权限不足
- `500`: 服务器内部错误

---

## 1. 认证管理接口

### 1.1 用户登录

**接口描述**: 用户登录，支持用户名、手机号、邮箱登录

- **请求路径**: `/auth/login`
- **请求方式**: `POST`
- **权限要求**: 无需认证
- **Content-Type**: `application/json`

#### 请求参数

```json
{
    "username": "root",
    "password": "admin123456",
    "verifyCode": "123456",
    "verifyType": "sms",
    "rememberMe": false
}
```

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| username | String | 是 | 用户名/手机号/邮箱 | "root" |
| password | String | 是 | 密码 | "admin123456" |
| verifyCode | String | 否 | 验证码 | "123456" |
| verifyType | String | 否 | 验证码类型(sms/email) | "sms" |
| rememberMe | Boolean | 否 | 记住我 | false |

#### 响应示例

**成功响应 (200)**:
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "tokenType": "Bearer",
        "expiresAt": "2025-07-15T18:30:00",
        "userInfo": {
            "userId": 1,
            "username": "root",
            "realName": "系统管理员",
            "mobile": "138****0000",
            "email": "a***@jnl.com",
            "permissionLevel": "root",
            "permissionName": "超级管理员",
            "avatar": null,
            "lastLoginTime": "2025-07-15T10:30:00"
        }
    },
    "timestamp": "2025-07-15T10:30:00"
}
```

**失败响应 (400)**:
```json
{
    "code": 400,
    "message": "用户名或密码错误",
    "data": null,
    "timestamp": "2025-07-15T10:30:00"
}
```

### 1.2 用户注册

**接口描述**: 新用户注册，默认权限为operator

- **请求路径**: `/auth/register`
- **请求方式**: `POST`
- **权限要求**: 无需认证
- **Content-Type**: `application/json`

#### 请求参数

```json
{
    "username": "testuser",
    "password": "password123",
    "confirmPassword": "password123",
    "mobile": "13800138000",
    "email": "<EMAIL>",
    "realName": "测试用户",
    "smsCode": "123456",
    "emailCode": "123456"
}
```

| 参数名 | 类型 | 必填 | 说明 | 验证规则 |
|--------|------|------|------|----------|
| username | String | 是 | 用户名 | 4-20位字母数字下划线 |
| password | String | 是 | 密码 | 8-32位包含字母数字 |
| confirmPassword | String | 是 | 确认密码 | 必须与password一致 |
| mobile | String | 是 | 手机号 | 中国大陆手机号格式 |
| email | String | 否 | 邮箱 | 标准邮箱格式 |
| realName | String | 否 | 真实姓名 | 最大50字符 |
| smsCode | String | 是 | 短信验证码 | 6位数字 |
| emailCode | String | 否 | 邮箱验证码 | 6位数字 |

#### 响应示例

**成功响应 (200)**:
```json
{
    "code": 200,
    "message": "注册成功",
    "data": {
        "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "tokenType": "Bearer",
        "expiresAt": "2025-07-15T18:30:00",
        "userInfo": {
            "userId": 2,
            "username": "testuser",
            "realName": "测试用户",
            "mobile": "138****8000",
            "email": "t***@example.com",
            "permissionLevel": "operator",
            "permissionName": "操作员",
            "avatar": null,
            "lastLoginTime": null
        }
    },
    "timestamp": "2025-07-15T10:30:00"
}
```

### 1.3 发送验证码

**接口描述**: 发送短信或邮箱验证码

- **请求路径**: `/auth/send-code`
- **请求方式**: `POST`
- **权限要求**: 无需认证
- **Content-Type**: `application/json`

#### 请求参数

```json
{
    "type": "sms",
    "mobile": "13800138000",
    "email": "<EMAIL>",
    "scene": "register"
}
```

| 参数名 | 类型 | 必填 | 说明 | 可选值 |
|--------|------|------|------|--------|
| type | String | 是 | 验证码类型 | sms, email |
| mobile | String | 条件 | 手机号(type=sms时必填) | 中国大陆手机号 |
| email | String | 条件 | 邮箱(type=email时必填) | 标准邮箱格式 |
| scene | String | 是 | 业务场景 | register, login, reset_password |

#### 响应示例

**成功响应 (200)**:
```json
{
    "code": 200,
    "message": "验证码发送成功",
    "data": null,
    "timestamp": "2025-07-15T10:30:00"
}
```

### 1.4 用户登出

**接口描述**: 用户退出登录

- **请求路径**: `/auth/logout`
- **请求方式**: `POST`
- **权限要求**: 需要Token
- **Headers**: `Authorization: Bearer <token>`

#### 响应示例

**成功响应 (200)**:
```json
{
    "code": 200,
    "message": "登出成功",
    "data": null,
    "timestamp": "2025-07-15T10:30:00"
}
```

### 1.5 刷新Token

**接口描述**: 刷新访问令牌

- **请求路径**: `/auth/refresh-token`
- **请求方式**: `POST`
- **权限要求**: 需要Token
- **Headers**: `Authorization: Bearer <token>`

#### 响应示例

**成功响应 (200)**:
```json
{
    "code": 200,
    "message": "Token刷新成功",
    "data": {
        "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "tokenType": "Bearer",
        "expiresAt": "2025-07-15T18:30:00",
        "userInfo": {
            "userId": 1,
            "username": "root",
            "realName": "系统管理员",
            "mobile": "138****0000",
            "email": "a***@jnl.com",
            "permissionLevel": "root",
            "permissionName": "超级管理员",
            "avatar": null,
            "lastLoginTime": "2025-07-15T10:30:00"
        }
    },
    "timestamp": "2025-07-15T10:30:00"
}
```

### 1.6 验证Token

**接口描述**: 验证访问令牌是否有效

- **请求路径**: `/auth/validate-token`
- **请求方式**: `GET`
- **权限要求**: 需要Token
- **Headers**: `Authorization: Bearer <token>`

#### 响应示例

**成功响应 (200)**:
```json
{
    "code": 200,
    "message": "Token有效",
    "data": true,
    "timestamp": "2025-07-15T10:30:00"
}
```

### 1.7 获取当前用户信息

**接口描述**: 根据Token获取当前登录用户信息

- **请求路径**: `/auth/current-user`
- **请求方式**: `GET`
- **权限要求**: 需要Token
- **Headers**: `Authorization: Bearer <token>`

#### 响应示例

**成功响应 (200)**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "userId": 1,
        "username": "root",
        "realName": "系统管理员",
        "mobile": "138****0000",
        "email": "a***@jnl.com",
        "permissionLevel": "root",
        "permissionName": "超级管理员",
        "avatar": null,
        "lastLoginTime": "2025-07-15T10:30:00"
    },
    "timestamp": "2025-07-15T10:30:00"
}
```

### 1.8 修改密码

**接口描述**: 用户修改登录密码

- **请求路径**: `/auth/change-password`
- **请求方式**: `POST`
- **权限要求**: 需要Token
- **Headers**: `Authorization: Bearer <token>`
- **Content-Type**: `application/x-www-form-urlencoded`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| oldPassword | String | 是 | 旧密码 |
| newPassword | String | 是 | 新密码 |

#### 响应示例

**成功响应 (200)**:
```json
{
    "code": 200,
    "message": "密码修改成功",
    "data": null,
    "timestamp": "2025-07-15T10:30:00"
}
```

### 1.9 重置密码

**接口描述**: 通过短信验证码重置密码

- **请求路径**: `/auth/reset-password`
- **请求方式**: `POST`
- **权限要求**: 无需认证
- **Content-Type**: `application/x-www-form-urlencoded`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| mobile | String | 是 | 手机号 |
| smsCode | String | 是 | 短信验证码 |
| newPassword | String | 是 | 新密码 |

#### 响应示例

**成功响应 (200)**:
```json
{
    "code": 200,
    "message": "密码重置成功",
    "data": null,
    "timestamp": "2025-07-15T10:30:00"
}
```

---

## 2. 权限管理接口

### 2.1 授权用户权限

**接口描述**: 只有root用户才能授权其他用户权限

- **请求路径**: `/permission/grant`
- **请求方式**: `POST`
- **权限要求**: root权限
- **Headers**: `Authorization: Bearer <root-token>`
- **Content-Type**: `application/x-www-form-urlencoded`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 可选值 |
|--------|------|------|------|--------|
| targetUserId | Integer | 是 | 目标用户ID | 正整数 |
| permissionLevel | String | 是 | 权限等级 | root, admin, operator, guest |

#### 响应示例

**成功响应 (200)**:
```json
{
    "code": 200,
    "message": "权限授权成功",
    "data": null,
    "timestamp": "2025-07-15T10:30:00"
}
```

**权限不足 (401)**:
```json
{
    "code": 401,
    "message": "只有root用户才能授权",
    "data": null,
    "timestamp": "2025-07-15T10:30:00"
}
```
