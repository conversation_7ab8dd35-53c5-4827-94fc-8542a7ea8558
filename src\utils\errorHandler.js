/**
 * @description 企业级错误处理工具
 * <AUTHOR> Team
 */

import { ElMessage, ElNotification } from "element-plus";

/**
 * @description 错误类型枚举
 */
export const ERROR_TYPES = {
  NETWORK: 'NETWORK',           // 网络错误
  AUTH: 'AUTH',                 // 认证错误
  PERMISSION: 'PERMISSION',     // 权限错误
  VALIDATION: 'VALIDATION',     // 验证错误
  BUSINESS: 'BUSINESS',         // 业务错误
  SYSTEM: 'SYSTEM',            // 系统错误
  JAVASCRIPT: 'JAVASCRIPT',     // JavaScript错误
  UNKNOWN: 'UNKNOWN'           // 未知错误
};

/**
 * @description 错误级别枚举
 */
export const ERROR_LEVELS = {
  LOW: 'LOW',       // 低级别 - 用户操作错误
  MEDIUM: 'MEDIUM', // 中级别 - 业务逻辑错误
  HIGH: 'HIGH',     // 高级别 - 系统错误
  CRITICAL: 'CRITICAL' // 严重级别 - 安全相关错误
};

/**
 * @description JavaScript错误映射
 */
const JS_ERROR_MAP = {
  InternalError: "Javascript引擎内部错误",
  ReferenceError: "未找到对象",
  TypeError: "使用了错误的类型或对象",
  RangeError: "使用内置对象时，参数超范围",
  SyntaxError: "语法错误",
  EvalError: "错误的使用了Eval",
  URIError: "URI错误"
};

/**
 * @description HTTP错误映射
 */
const HTTP_ERROR_MAP = {
  400: '请求参数错误',
  401: '登录状态已过期，请重新登录',
  403: '权限不足，无法访问该资源',
  404: '请求的资源不存在',
  500: '服务器内部错误',
  502: '网关错误',
  503: '服务暂时不可用'
};

/**
 * @description 自定义错误类
 */
export class AppError extends Error {
  constructor(message, type = ERROR_TYPES.UNKNOWN, level = ERROR_LEVELS.MEDIUM, code = null) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.level = level;
    this.code = code;
    this.timestamp = new Date().toISOString();
  }
}

/**
 * @description 全局错误处理函数 (兼容旧版本)
 */
const errorHandler = error => {
  console.log({ error });

  // 过滤 HTTP 请求错误
  if (error.status || error.status === 0) return false;

  let errorName = JS_ERROR_MAP[error.name] || "未知错误";

  ElNotification({
    title: errorName,
    message: error.message || '页面出现异常，请刷新页面重试',
    type: "error",
    duration: 3000
  });
};

/**
 * @description 全局错误处理函数
 * @param {Error} error 错误对象
 * @param {Object} context 错误上下文
 */
export const handleError = (error, context = {}) => {
  if (error instanceof AppError) {
    // 自定义错误
    if (error.level === ERROR_LEVELS.CRITICAL) {
      ElNotification({
        title: '系统错误',
        message: error.message,
        type: 'error',
        duration: 0,
        showClose: true
      });
    } else {
      ElMessage.error(error.message);
    }
  } else {
    // 其他错误使用原有逻辑
    errorHandler(error);
  }
};

/**
 * @description 创建业务错误
 */
export const createBusinessError = (message, code) => {
  return new AppError(message, ERROR_TYPES.BUSINESS, ERROR_LEVELS.MEDIUM, code);
};

/**
 * @description 创建验证错误
 */
export const createValidationError = (message) => {
  return new AppError(message, ERROR_TYPES.VALIDATION, ERROR_LEVELS.LOW);
};

/**
 * @description 创建权限错误
 */
export const createPermissionError = (message) => {
  return new AppError(message, ERROR_TYPES.PERMISSION, ERROR_LEVELS.MEDIUM);
};

export default errorHandler;
