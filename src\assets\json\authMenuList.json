{"code": 200, "data": [{"path": "/home/<USER>", "name": "home", "component": "/home/<USER>", "meta": {"icon": "HomeFilled", "title": "首页", "isLink": "", "isHide": false, "isFull": false, "isAffix": true, "isKeepAlive": true}}, {"path": "/dataScreen", "name": "dataScreen", "component": "/dataScreen/index", "meta": {"icon": "Histogram", "title": "数据大屏", "isLink": "", "isHide": false, "isFull": true, "isAffix": false, "isKeepAlive": true}}, {"path": "/proTable", "name": "proTable", "redirect": "/proTable/useProTable", "meta": {"icon": "MessageBox", "title": "超级表格", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}, "children": [{"path": "/proTable/useProTable", "name": "useProTable", "component": "/proTable/useProTable/index", "meta": {"icon": "ShoppingBag", "title": "商品管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}, "children": [{"path": "/proTable/useProTable/detail/:id", "name": "useProTableDetail", "component": "/proTable/useProTable/detail", "meta": {"icon": "<PERSON><PERSON>", "title": "产品 详情", "activeMenu": "/proTable/useProTable", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isKeepAlive": true}}]}, {"path": "/proTable/useTreeFilter", "name": "useTreeFilter", "component": "/proTable/useTreeFilter/index", "meta": {"icon": "<PERSON><PERSON>", "title": "编码树", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/proTable/useTreeFilter/detail/:id", "name": "useTreeFilterDetail", "component": "/proTable/useTreeFilter/detail", "meta": {"icon": "<PERSON><PERSON>", "title": "分类 详情", "activeMenu": "/proTable/useTreeFilter", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/proTable/useSelectFilter", "name": "useSelectFilter", "component": "/proTable/useSelectFilter/index", "meta": {"icon": "ShoppingCart", "title": "订单", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/proTable/treeProTable", "name": "treeProTable", "component": "/proTable/treeProTable/index", "meta": {"icon": "<PERSON><PERSON>", "title": "产品分类树", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/proTable/complexProTable", "name": "complexProTable", "component": "/proTable/complexProTable/index", "meta": {"icon": "<PERSON><PERSON>", "title": "复杂 ProTable", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/proTable/document", "name": "proTableDocument", "component": "/proTable/document/index", "meta": {"icon": "<PERSON><PERSON>", "title": "ProTable 文档", "isLink": "https://juejin.cn/post/7166068828202336263/#heading-14", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}]}, {"path": "/auth", "name": "auth", "redirect": "/auth/menu", "meta": {"icon": "Lock", "title": "数据上传", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}, "children": [{"path": "/auth/menu", "name": "authMenu", "component": "/auth/menu/index", "meta": {"icon": "<PERSON><PERSON>", "title": "sku上传", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/auth/button", "name": "authButton", "component": "/auth/button/index", "meta": {"icon": "<PERSON><PERSON>", "title": "按钮权限", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/auth/oss", "name": "oss", "component": "/auth/oss/index", "meta": {"icon": "<PERSON><PERSON>", "title": "OSS地址", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}]}, {"path": "/assembly", "name": "assembly", "redirect": "/assembly/guide", "meta": {"icon": "Briefcase", "title": "常用组件", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}, "children": [{"path": "/assembly/guide", "name": "guide", "component": "/assembly/guide/index", "meta": {"icon": "<PERSON><PERSON>", "title": "引导页", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/assembly/tabs", "name": "tabs", "component": "/assembly/tabs/index", "meta": {"icon": "<PERSON><PERSON>", "title": "标签页操作", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}, "children": [{"path": "/assembly/tabs/detail/:id", "name": "tabsDetail", "component": "/assembly/tabs/detail", "meta": {"icon": "<PERSON><PERSON>", "title": "Tab 详情", "activeMenu": "/assembly/tabs", "isLink": "", "isHide": true, "isFull": false, "isAffix": false, "isKeepAlive": true}}]}, {"path": "/assembly/selectIcon", "name": "selectIcon", "component": "/assembly/selectIcon/index", "meta": {"icon": "<PERSON><PERSON>", "title": "图标选择器", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/assembly/selectFilter", "name": "selectFilter", "component": "/assembly/selectFilter/index", "meta": {"icon": "<PERSON><PERSON>", "title": "分类筛选器", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/assembly/treeFilter", "name": "treeFilter", "component": "/assembly/treeFilter/index", "meta": {"icon": "<PERSON><PERSON>", "title": "树形筛选器", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/assembly/svgIcon", "name": "svgIcon", "component": "/assembly/svgIcon/index", "meta": {"icon": "<PERSON><PERSON>", "title": "SVG 图标", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/assembly/uploadFile", "name": "uploadFile", "component": "/assembly/uploadFile/index", "meta": {"icon": "<PERSON><PERSON>", "title": "文件上传", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/assembly/batchImport", "name": "batchImport", "component": "/assembly/batchImport/index", "meta": {"icon": "<PERSON><PERSON>", "title": "批量添加数据", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/assembly/wangEditor", "name": "wang<PERSON><PERSON><PERSON>", "component": "/assembly/wangEditor/index", "meta": {"icon": "<PERSON><PERSON>", "title": "富文本编辑器", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/assembly/draggable", "name": "draggable", "component": "/assembly/draggable/index", "meta": {"icon": "<PERSON><PERSON>", "title": "拖拽组件", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}]}, {"path": "/dashboard", "name": "dashboard", "redirect": "/dashboard/dataVisualize", "meta": {"icon": "Odometer", "title": "Dashboard", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}, "children": [{"path": "/dashboard/dataVisualize", "name": "dataVisualize", "component": "/dashboard/dataVisualize/index", "meta": {"icon": "<PERSON><PERSON>", "title": "数据可视化", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}]}, {"path": "/form", "name": "form", "redirect": "/form/proForm", "meta": {"icon": "Tickets", "title": "表单 Form", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}, "children": [{"path": "/form/proForm", "name": "proForm", "component": "/form/proForm/index", "meta": {"icon": "<PERSON><PERSON>", "title": "超级 Form", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/form/basicForm", "name": "basicForm", "component": "/form/basicForm/index", "meta": {"icon": "<PERSON><PERSON>", "title": "基础 Form", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/form/validateForm", "name": "validateForm", "component": "/form/validateForm/index", "meta": {"icon": "<PERSON><PERSON>", "title": "校验 Form", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/form/dynamicForm", "name": "dynamicForm", "component": "/form/dynamicForm/index", "meta": {"icon": "<PERSON><PERSON>", "title": "动态 Form", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}]}, {"path": "/echarts", "name": "echarts", "redirect": "/echarts/waterChart", "meta": {"icon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}, "children": [{"path": "/echarts/waterChart", "name": "waterChart", "component": "/echarts/waterChart/index", "meta": {"icon": "<PERSON><PERSON>", "title": "水型图", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/echarts/columnChart", "name": "columnChart", "component": "/echarts/columnChart/index", "meta": {"icon": "<PERSON><PERSON>", "title": "柱状图", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/echarts/lineChart", "name": "lineChart", "component": "/echarts/lineChart/index", "meta": {"icon": "<PERSON><PERSON>", "title": "折线图", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/echarts/pie<PERSON>hart", "name": "<PERSON><PERSON><PERSON>", "component": "/echarts/pieChart/index", "meta": {"icon": "<PERSON><PERSON>", "title": "饼图", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/echarts/radarChart", "name": "radarChart", "component": "/echarts/radarChart/index", "meta": {"icon": "<PERSON><PERSON>", "title": "雷达图", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/echarts/nested<PERSON>hart", "name": "nested<PERSON><PERSON>", "component": "/echarts/nestedChart/index", "meta": {"icon": "<PERSON><PERSON>", "title": "嵌套环形图", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}]}, {"path": "/directives", "name": "directives", "redirect": "/directives/copyDirect", "meta": {"icon": "Stamp", "title": "自定义指令", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}, "children": [{"path": "/directives/copyDirect", "name": "copyDirect", "component": "/directives/copyDirect/index", "meta": {"icon": "<PERSON><PERSON>", "title": "复制指令", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/directives/watermarkDirect", "name": "watermarkDirect", "component": "/directives/watermarkDirect/index", "meta": {"icon": "<PERSON><PERSON>", "title": "水印指令", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/directives/dragDirect", "name": "dragDirect", "component": "/directives/dragDirect/index", "meta": {"icon": "<PERSON><PERSON>", "title": "拖拽指令", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/directives/debounceDirect", "name": "debounceDirect", "component": "/directives/debounceDirect/index", "meta": {"icon": "<PERSON><PERSON>", "title": "防抖指令", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/directives/throttleDirect", "name": "throttleDirect", "component": "/directives/throttleDirect/index", "meta": {"icon": "<PERSON><PERSON>", "title": "节流指令", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/directives/longpressDirect", "name": "longpressDirect", "component": "/directives/longpressDirect/index", "meta": {"icon": "<PERSON><PERSON>", "title": "长按指令", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}]}, {"path": "/menu", "name": "menu", "redirect": "/menu/menu1", "meta": {"icon": "List", "title": "菜单嵌套", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}, "children": [{"path": "/menu/menu1", "name": "menu1", "component": "/menu/menu1/index", "meta": {"icon": "<PERSON><PERSON>", "title": "菜单1", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/menu/menu2", "name": "menu2", "redirect": "/menu/menu2/menu21", "meta": {"icon": "<PERSON><PERSON>", "title": "菜单2", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}, "children": [{"path": "/menu/menu2/menu21", "name": "menu21", "component": "/menu/menu2/menu21/index", "meta": {"icon": "<PERSON><PERSON>", "title": "菜单2-1", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/menu/menu2/menu22", "name": "menu22", "redirect": "/menu/menu2/menu22/menu221", "meta": {"icon": "<PERSON><PERSON>", "title": "菜单2-2", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}, "children": [{"path": "/menu/menu2/menu22/menu221", "name": "menu221", "component": "/menu/menu2/menu22/menu221/index", "meta": {"icon": "<PERSON><PERSON>", "title": "菜单2-2-1", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/menu/menu2/menu22/menu222", "name": "menu222", "component": "/menu/menu2/menu22/menu222/index", "meta": {"icon": "<PERSON><PERSON>", "title": "菜单2-2-2", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}]}, {"path": "/menu/menu2/menu23", "name": "menu23", "component": "/menu/menu2/menu23/index", "meta": {"icon": "<PERSON><PERSON>", "title": "菜单2-3", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}]}, {"path": "/menu/menu3", "name": "menu3", "component": "/menu/menu3/index", "meta": {"icon": "<PERSON><PERSON>", "title": "菜单3", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}]}, {"path": "/system", "name": "system", "redirect": "/system/accountManage", "meta": {"icon": "Tools", "title": "系统管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}, "children": [{"path": "/system/accountManage", "name": "accountManage", "component": "/system/accountManage/index", "meta": {"icon": "<PERSON><PERSON>", "title": "账号管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/system/roleManage", "name": "roleManage", "component": "/system/roleManage/index", "meta": {"icon": "<PERSON><PERSON>", "title": "角色管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/system/menuMange", "name": "menuMange", "component": "/system/menuMange/index", "meta": {"icon": "<PERSON><PERSON>", "title": "菜单管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/system/departmentManage", "name": "departmentManage", "component": "/system/departmentManage/index", "meta": {"icon": "<PERSON><PERSON>", "title": "部门管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/system/dictManage", "name": "dictManage", "component": "/system/dictManage/index", "meta": {"icon": "<PERSON><PERSON>", "title": "字典管理", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/system/timingTask", "name": "timingTask", "component": "/system/timingTask/index", "meta": {"icon": "<PERSON><PERSON>", "title": "定时任务", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/system/systemLog", "name": "systemLog", "component": "/system/systemLog/index", "meta": {"icon": "<PERSON><PERSON>", "title": "系统日志", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}]}, {"path": "/link", "name": "link", "redirect": "/link/bing", "meta": {"icon": "Paperclip", "title": "外部链接", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}, "children": [{"path": "/link/bing", "name": "bing", "component": "/link/bing/index", "meta": {"icon": "<PERSON><PERSON>", "title": "Bing 内嵌", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/link/gitee", "name": "gitee", "component": "/link/gitee/index", "meta": {"icon": "<PERSON><PERSON>", "title": "Gitee 仓库", "isLink": "https://gitee.com/HalseySpicy/Geeker-Admin", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/link/github", "name": "github", "component": "/link/github/index", "meta": {"icon": "<PERSON><PERSON>", "title": "GitHub 仓库", "isLink": "https://github.com/HalseySpicy/Geeker-Admin", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/link/docs", "name": "docs", "component": "/link/docs/index", "meta": {"icon": "<PERSON><PERSON>", "title": "项目文档", "isLink": "https://docs.spicyboy.cn", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}, {"path": "/link/juejin", "name": "jue<PERSON>", "component": "/link/juejin/index", "meta": {"icon": "<PERSON><PERSON>", "title": "掘金主页", "isLink": "https://juejin.cn/user/3263814531551816/posts", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}]}, {"path": "/about/index", "name": "about", "component": "/about/index", "meta": {"icon": "InfoFilled", "title": "关于项目", "isLink": "", "isHide": false, "isFull": false, "isAffix": false, "isKeepAlive": true}}], "msg": "成功"}