import axios from "axios";
import { showFullScreenLoading, tryHideFullScreenLoading } from "@/components/Loading/fullScreen";
import { LOGIN_URL } from "@/config";
import { ElMessage, ElMessageBox } from "element-plus";
import { checkStatus } from "./helper/checkStatus";
import { AxiosCanceler } from "./helper/axiosCancel";
import { useUserStore } from "@/stores/modules/user";
import { useAuthStore } from "@/stores/modules/auth";
import { handleError } from "@/utils/errorHandler";
import router from "@/routers";

// 企业级HTTP配置
const config = {
  // 默认地址请求地址，可在 .env.** 文件中修改
  baseURL: import.meta.env.VITE_API_URL,
  // 设置超时时间 (30秒)
  timeout: 30000,
  // 跨域时候允许携带凭证
  withCredentials: true,
  // 请求头配置
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
};

const axiosCanceler = new AxiosCanceler();

// Token刷新相关变量
let isRefreshing = false;
let failedQueue = [];

// 处理队列中的请求
const processQueue = (error, token = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });

  failedQueue = [];
};

class RequestHttp {
  constructor(config) {
    // 创建axios实例
    this.service = axios.create(config);

    /**
     * @description 请求拦截器
     * 客户端发送请求 -> [请求拦截器] -> 服务器
     * JWT Token认证 + 企业级安全配置
     */
    this.service.interceptors.request.use(
      config => {
        const userStore = useUserStore();

        // 重复请求控制
        config.cancel = config.cancel !== false;
        config.cancel && axiosCanceler.addPending(config);

        // Loading控制
        config.loading = config.loading !== false;
        config.loading && showFullScreenLoading();

        // JWT Token认证 - 符合JNL-Shop API规范
        const token = userStore.token;
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // 请求ID追踪 (企业级日志追踪)
        config.headers['X-Request-ID'] = this.generateRequestId();

        // 时间戳 (防重放攻击)
        config.headers['X-Timestamp'] = Date.now().toString();

        // 客户端信息
        config.headers['X-Client-Version'] = import.meta.env.VITE_APP_VERSION || '1.0.0';

        return config;
      },
      error => {
        tryHideFullScreenLoading();
        return Promise.reject(error);
      }
    );

    /**
     * @description 响应拦截器
     * 服务器返回信息 -> [拦截统一处理] -> 客户端JS获取到信息
     * 符合JNL-Shop API规范的企业级响应处理
     */
    this.service.interceptors.response.use(
      async response => {
        const { data, config } = response;
        const userStore = useUserStore();
        const authStore = useAuthStore();

        // 清理请求相关状态
        axiosCanceler.removePending(config);
        config.loading && tryHideFullScreenLoading();

        try {

        // 处理文件下载等特殊响应
        const contentType = response.headers['content-type'] || '';
        if (contentType.includes('application/octet-stream') ||
            contentType.includes('application/vnd.ms-excel') ||
            contentType.includes('application/pdf') ||
            contentType.includes('text/plain') ||
            contentType.includes('text/html')) {
          return response;
        }

        // 处理空响应或非对象响应
        if (data === null || data === undefined) {
          return { code: 200, data: null, message: 'success' };
        }

        // 处理非对象响应（如字符串、数字等）
        if (typeof data !== 'object') {
          return { code: 200, data: data, message: 'success' };
        }

        // 根据JNL-Shop API规范处理响应码
        // 如果没有code字段，认为是成功响应（兼容不同的API格式）
        if (typeof data.code === 'undefined') {
          return data;
        }

        // 处理响应码
        const code = Number(data.code);
        const message = data.message || data.msg || '';

        switch (code) {
          case 200:
            // 成功响应
            return data;

          case 401:
            // Token无效或过期 - 尝试刷新Token
            return this.handleTokenExpired(config, userStore, authStore);

          case 403:
            // 权限不足
            ElMessage.error(message || '权限不足，无法访问该资源');
            return Promise.reject(data);

          case 400:
            // 请求参数错误
            ElMessage.error(message || '请求参数错误');
            return Promise.reject(data);

          case 500:
            // 服务器内部错误
            ElMessage.error(message || '服务器内部错误，请稍后重试');
            return Promise.reject(data);

          default:
            // 其他错误码
            if (code !== 200) {
              ElMessage.error(message || `请求失败 (${code})`);
              return Promise.reject(data);
            }
            return data;
        }
        } catch (error) {
          // 响应处理过程中的错误
          console.error('响应拦截器处理错误:', error);
          ElMessage.error('响应处理失败');
          return Promise.reject(error);
        }
      },
      async error => {
        const { response, config } = error;

        // 清理状态
        tryHideFullScreenLoading();
        config && axiosCanceler.removePending(config);

        // 网络错误处理
        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
          ElMessage.error('请求超时，请检查网络连接后重试');
          return Promise.reject(error);
        }

        if (error.message.includes('Network Error')) {
          ElMessage.error('网络连接失败，请检查网络设置');
          return Promise.reject(error);
        }

        // 请求被取消
        if (axios.isCancel(error)) {
          console.log('Request canceled:', error.message);
          return Promise.reject(error);
        }

        // HTTP状态码错误处理
        if (response) {
          const { status, data } = response;

          // 根据HTTP状态码处理
          switch (status) {
            case 401:
              // 未授权 - 清除用户信息并跳转登录
              this.handleUnauthorized();
              break;
            case 403:
              ElMessage.error('权限不足，拒绝访问');
              break;
            case 404:
              ElMessage.error('请求的资源不存在');
              break;
            case 500:
              ElMessage.error('服务器内部错误');
              break;
            default:
              // 显示后端返回的错误信息
              const errorMsg = data?.message || `请求失败 (HTTP ${status})`;
              ElMessage.error(errorMsg);
          }

          // 调用状态检查函数
          checkStatus(status);
        }

        // 离线检测
        if (!window.navigator.onLine) {
          ElMessage.error('网络连接已断开，请检查网络设置');
          router.replace("/500");
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * @description 生成请求ID (企业级日志追踪)
   */
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * @description 处理Token过期 - 自动刷新Token
   */
  async handleTokenExpired(originalConfig, userStore, authStore) {
    if (isRefreshing) {
      // 如果正在刷新，将请求加入队列
      return new Promise((resolve, reject) => {
        failedQueue.push({ resolve, reject });
      }).then(token => {
        originalConfig.headers.Authorization = `Bearer ${token}`;
        return this.service(originalConfig);
      });
    }

    isRefreshing = true;

    try {
      // 调用刷新Token接口
      const response = await this.service.post('/auth/refresh-token');
      const { accessToken, userInfo } = response.data;

      // 更新Token和用户信息
      userStore.setToken(accessToken);
      userStore.setUserInfo(userInfo);
      authStore.setPermissionLevel(userInfo.permissionLevel);

      // 处理队列中的请求
      processQueue(null, accessToken);

      // 重新发送原始请求
      originalConfig.headers.Authorization = `Bearer ${accessToken}`;
      return this.service(originalConfig);

    } catch (error) {
      // Token刷新失败，清除用户信息并跳转登录
      processQueue(error, null);
      this.handleUnauthorized();
      return Promise.reject(error);
    } finally {
      isRefreshing = false;
    }
  }

  /**
   * @description 处理未授权 - 清除用户信息并跳转登录
   */
  handleUnauthorized() {
    const userStore = useUserStore();
    const authStore = useAuthStore();

    // 清除用户信息
    userStore.setToken("");
    userStore.setUserInfo({});
    authStore.setPermissionLevel("");

    // 显示提示信息
    ElMessageBox.confirm(
      '登录状态已过期，请重新登录',
      '系统提示',
      {
        confirmButtonText: '重新登录',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false,
        closeOnPressEscape: false
      }
    ).then(() => {
      router.replace(LOGIN_URL);
    }).catch(() => {
      router.replace(LOGIN_URL);
    });
  }

  /**
   * @description 常用请求方法封装
   */
  /**
   * @description GET请求
   */
  get(url, params, config = {}) {
    return this.service.get(url, { params, ...config });
  }

  /**
   * @description POST请求
   */
  post(url, data, config = {}) {
    return this.service.post(url, data, config);
  }

  /**
   * @description PUT请求
   */
  put(url, data, config = {}) {
    return this.service.put(url, data, config);
  }

  /**
   * @description DELETE请求
   */
  delete(url, params, config = {}) {
    return this.service.delete(url, { params, ...config });
  }

  /**
   * @description PATCH请求
   */
  patch(url, data, config = {}) {
    return this.service.patch(url, data, config);
  }

  /**
   * @description 文件下载
   */
  download(url, data, config = {}) {
    return this.service.post(url, data, {
      ...config,
      responseType: "blob",
      loading: config.loading !== false
    });
  }

  /**
   * @description 文件上传
   */
  upload(url, formData, config = {}) {
    return this.service.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config.headers
      }
    });
  }

  /**
   * @description 表单提交 (application/x-www-form-urlencoded)
   */
  postForm(url, data, config = {}) {
    return this.service.post(url, data, {
      ...config,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        ...config.headers
      }
    });
  }
}

export default new RequestHttp(config);
