<template>
  <div class="transaction-chart">
    <div ref="transactionChartRef" class="chart-container"></div>
  </div>
</template>

<script setup name="TransactionChart">
import { ref, onMounted, onBeforeUnmount } from "vue";
import * as echarts from "echarts";
import { getTransactionDataApi } from "@/api/modules/dataScreen";

const transactionChartRef = ref();
let myChart = null;

// 模拟数据
const mockData = {
  transactions: 1256,
  refunds: 89,
  returns: 45
};

const initChart = () => {
  myChart = echarts.init(transactionChartRef.value);
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#667eea',
      borderWidth: 1,
      textStyle: {
        color: '#2c3e50'
      }
    },
    legend: {
      orient: 'horizontal',
      bottom: '10%',
      left: 'center',
      textStyle: {
        color: '#2c3e50',
        fontSize: 12
      },
      data: ['成交量', '退款量', '退货量']
    },
    series: [
      {
        name: '交易统计',
        type: 'pie',
        radius: ['45%', '75%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}\n{d}%',
          fontSize: 11,
          color: '#2c3e50'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10,
          lineStyle: {
            color: '#2c3e50'
          }
        },
        data: [
          {
            value: mockData.transactions,
            name: '成交量',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 1,
                colorStops: [
                  { offset: 0, color: '#667eea' },
                  { offset: 1, color: '#764ba2' }
                ]
              }
            }
          },
          {
            value: mockData.refunds,
            name: '退款量',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 1,
                colorStops: [
                  { offset: 0, color: '#f093fb' },
                  { offset: 1, color: '#f5576c' }
                ]
              }
            }
          },
          {
            value: mockData.returns,
            name: '退货量',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 1,
                colorStops: [
                  { offset: 0, color: '#4facfe' },
                  { offset: 1, color: '#00f2fe' }
                ]
              }
            }
          }
        ]
      }
    ]
  };
  
  myChart.setOption(option);
};

const getTransactionData = async () => {
  try {
    // 保留API调用接口，但暂时使用模拟数据
    // const { data } = await getTransactionDataApi();
    // 使用模拟数据
    console.log("获取交易数据:", mockData);
  } catch (error) {
    console.error("获取交易数据失败:", error);
  }
};

onMounted(() => {
  getTransactionData();
  initChart();
  
  window.addEventListener("resize", () => {
    if (myChart) {
      myChart.resize();
    }
  });
});

onBeforeUnmount(() => {
  if (myChart) {
    myChart.dispose();
  }
});
</script>

<style scoped lang="scss">
.transaction-chart {
  width: 100%;
  height: 100%;

  .chart-container {
    width: 100%;
    height: 100%;
    min-height: 200px;
  }
}
</style>
