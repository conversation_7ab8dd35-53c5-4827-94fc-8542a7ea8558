<template>
  <div class="revenue-chart">
    <div ref="revenueChartRef" class="chart-container"></div>
  </div>
</template>

<script setup name="RevenueChart">
import { ref, onMounted, onBeforeUnmount } from "vue";
import * as echarts from "echarts";
import { getRevenueDataApi } from "@/api/modules/dataScreen";

const revenueChartRef = ref();
let myChart = null;

// 模拟近15日数据
const mockData = {
  dates: [],
  revenues: [],
  totalRevenue: 0
};

// 生成近15日的模拟数据
const generateMockData = () => {
  const today = new Date();
  let total = 0;
  
  for (let i = 14; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    mockData.dates.push(`${date.getMonth() + 1}/${date.getDate()}`);
    
    // 生成随机收入数据 (10000-50000)
    const revenue = Math.floor(Math.random() * 40000) + 10000;
    mockData.revenues.push(revenue);
    total += revenue;
  }
  
  mockData.totalRevenue = total;
};

const initChart = () => {
  myChart = echarts.init(revenueChartRef.value);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        return `${params[0].name}<br/>成交金额: ¥${params[0].value.toLocaleString()}`;
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#667eea',
      borderWidth: 1,
      textStyle: {
        color: '#2c3e50'
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: mockData.dates,
      axisLabel: {
        color: '#7f8c8d',
        fontSize: 11
      },
      axisLine: {
        lineStyle: {
          color: '#ecf0f1'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#7f8c8d',
        fontSize: 11,
        formatter: function(value) {
          return '¥' + (value / 1000).toFixed(0) + 'k';
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#ecf0f1',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '成交金额',
        type: 'line',
        smooth: true,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(102, 126, 234, 0.3)' },
            { offset: 1, color: 'rgba(102, 126, 234, 0.05)' }
          ])
        },
        lineStyle: {
          color: '#667eea',
          width: 3,
          shadowColor: 'rgba(102, 126, 234, 0.3)',
          shadowBlur: 10,
          shadowOffsetY: 5
        },
        itemStyle: {
          color: '#667eea',
          borderColor: '#fff',
          borderWidth: 2
        },
        emphasis: {
          itemStyle: {
            color: '#764ba2',
            borderColor: '#fff',
            borderWidth: 3,
            shadowBlur: 10,
            shadowColor: 'rgba(118, 75, 162, 0.5)'
          }
        },
        data: mockData.revenues
      }
    ]
  };
  
  myChart.setOption(option);
};

const getRevenueData = async () => {
  try {
    // 保留API调用接口，但暂时使用模拟数据
    // const { data } = await getRevenueDataApi();
    generateMockData();
    console.log("获取收入数据:", mockData);
  } catch (error) {
    console.error("获取收入数据失败:", error);
  }
};

onMounted(() => {
  getRevenueData();
  initChart();
  
  window.addEventListener("resize", () => {
    if (myChart) {
      myChart.resize();
    }
  });
});

onBeforeUnmount(() => {
  if (myChart) {
    myChart.dispose();
  }
});
</script>

<style scoped lang="scss">
.revenue-chart {
  width: 100%;
  height: 100%;

  .chart-container {
    width: 100%;
    height: 100%;
    min-height: 250px;
  }
}
</style>
