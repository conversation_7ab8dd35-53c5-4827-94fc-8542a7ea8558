<template>
  <el-dialog
    v-model="dialogVisible"
    title="日志配置"
    width="600px"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <el-form :model="configForm" :rules="rules" ref="formRef" label-width="120px">
      <el-card shadow="hover" class="mb20">
        <template #header>
          <span>日志记录配置</span>
        </template>
        
        <el-form-item label="启用日志记录" prop="enabled">
          <el-switch v-model="configForm.enabled" />
          <div class="form-tip">关闭后将不再记录系统操作日志</div>
        </el-form-item>
        
        <el-form-item label="记录级别" prop="level">
          <el-select v-model="configForm.level" style="width: 100%">
            <el-option label="全部" value="ALL" />
            <el-option label="仅成功操作" value="SUCCESS" />
            <el-option label="仅失败操作" value="ERROR" />
            <el-option label="重要操作" value="IMPORTANT" />
          </el-select>
          <div class="form-tip">选择需要记录的操作级别</div>
        </el-form-item>
        
        <el-form-item label="记录模块" prop="modules">
          <el-select 
            v-model="configForm.modules" 
            multiple 
            style="width: 100%"
            placeholder="选择需要记录日志的模块"
          >
            <el-option
              v-for="module in moduleOptions"
              :key="module.value"
              :label="module.label"
              :value="module.value"
            />
          </el-select>
          <div class="form-tip">留空表示记录所有模块的操作</div>
        </el-form-item>
      </el-card>
      
      <el-card shadow="hover" class="mb20">
        <template #header>
          <span>存储配置</span>
        </template>
        
        <el-form-item label="自动清理" prop="autoClean">
          <el-switch v-model="configForm.autoClean" />
          <div class="form-tip">启用后将自动清理过期日志</div>
        </el-form-item>
        
        <el-form-item label="保留天数" prop="retentionDays" v-if="configForm.autoClean">
          <el-input-number
            v-model="configForm.retentionDays"
            :min="7"
            :max="365"
            style="width: 100%"
          />
          <div class="form-tip">超过此天数的日志将被自动删除</div>
        </el-form-item>
        
        <el-form-item label="清理时间" prop="cleanTime" v-if="configForm.autoClean">
          <el-time-picker
            v-model="configForm.cleanTime"
            format="HH:mm"
            value-format="HH:mm"
            style="width: 100%"
          />
          <div class="form-tip">每日自动清理的执行时间</div>
        </el-form-item>
      </el-card>
      
      <el-card shadow="hover" class="mb20">
        <template #header>
          <span>性能配置</span>
        </template>
        
        <el-form-item label="异步记录" prop="asyncLogging">
          <el-switch v-model="configForm.asyncLogging" />
          <div class="form-tip">启用异步记录可提高系统性能</div>
        </el-form-item>
        
        <el-form-item label="批量大小" prop="batchSize" v-if="configForm.asyncLogging">
          <el-input-number
            v-model="configForm.batchSize"
            :min="10"
            :max="1000"
            style="width: 100%"
          />
          <div class="form-tip">批量写入数据库的记录数量</div>
        </el-form-item>
        
        <el-form-item label="缓存大小" prop="cacheSize">
          <el-input-number
            v-model="configForm.cacheSize"
            :min="100"
            :max="10000"
            style="width: 100%"
          />
          <div class="form-tip">内存中缓存的日志记录数量</div>
        </el-form-item>
      </el-card>
      
      <el-card shadow="hover">
        <template #header>
          <span>安全配置</span>
        </template>
        
        <el-form-item label="敏感信息过滤" prop="sensitiveFilter">
          <el-switch v-model="configForm.sensitiveFilter" />
          <div class="form-tip">自动过滤密码等敏感信息</div>
        </el-form-item>
        
        <el-form-item label="IP白名单" prop="ipWhitelist">
          <el-input
            v-model="configForm.ipWhitelist"
            type="textarea"
            :rows="3"
            placeholder="每行一个IP地址或IP段，如：***********/24"
          />
          <div class="form-tip">白名单内的IP地址不记录日志</div>
        </el-form-item>
        
        <el-form-item label="用户白名单" prop="userWhitelist">
          <el-select 
            v-model="configForm.userWhitelist" 
            multiple 
            filterable
            style="width: 100%"
            placeholder="选择不记录日志的用户"
          >
            <el-option
              v-for="user in userOptions"
              :key="user.value"
              :label="user.label"
              :value="user.value"
            />
          </el-select>
          <div class="form-tip">白名单内的用户操作不记录日志</div>
        </el-form-item>
      </el-card>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveConfig" :loading="saveLoading">保存配置</el-button>
        <el-button type="success" @click="testConfig" :loading="testLoading">测试配置</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";

// 对话框可见性
const dialogVisible = ref(false);

// 表单引用
const formRef = ref(null);

// 加载状态
const saveLoading = ref(false);
const testLoading = ref(false);

// 模块选项
const moduleOptions = ref([
  { label: '用户管理', value: 'user' },
  { label: '角色管理', value: 'role' },
  { label: '菜单管理', value: 'menu' },
  { label: '部门管理', value: 'department' },
  { label: '字典管理', value: 'dict' },
  { label: '文件管理', value: 'file' },
  { label: '系统日志', value: 'log' },
  { label: '商品管理', value: 'product' },
  { label: '订单管理', value: 'order' }
]);

// 用户选项
const userOptions = ref([
  { label: 'admin', value: 'admin' },
  { label: 'system', value: 'system' },
  { label: 'guest', value: 'guest' }
]);

// 配置表单
const configForm = reactive({
  enabled: true,
  level: 'ALL',
  modules: [],
  autoClean: true,
  retentionDays: 30,
  cleanTime: '02:00',
  asyncLogging: true,
  batchSize: 100,
  cacheSize: 1000,
  sensitiveFilter: true,
  ipWhitelist: '',
  userWhitelist: []
});

// 表单验证规则
const rules = {
  level: [
    { required: true, message: '请选择记录级别', trigger: 'change' }
  ],
  retentionDays: [
    { required: true, message: '请输入保留天数', trigger: 'blur' },
    { type: 'number', min: 7, max: 365, message: '保留天数必须在7-365天之间', trigger: 'blur' }
  ],
  cleanTime: [
    { required: true, message: '请选择清理时间', trigger: 'change' }
  ],
  batchSize: [
    { required: true, message: '请输入批量大小', trigger: 'blur' },
    { type: 'number', min: 10, max: 1000, message: '批量大小必须在10-1000之间', trigger: 'blur' }
  ],
  cacheSize: [
    { required: true, message: '请输入缓存大小', trigger: 'blur' },
    { type: 'number', min: 100, max: 10000, message: '缓存大小必须在100-10000之间', trigger: 'blur' }
  ]
};

/**
 * 加载配置
 */
const loadConfig = async () => {
  try {
    // 这里应该调用获取配置的API
    // const { data } = await getLogConfig();
    // Object.assign(configForm, data);
    
    // 模拟加载配置
    ElMessage.success('配置加载成功');
  } catch (error) {
    ElMessage.error('加载配置失败');
  }
};

/**
 * 保存配置
 */
const saveConfig = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) return;
    
    saveLoading.value = true;
    
    // 这里应该调用保存配置的API
    // await saveLogConfig(configForm);
    
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    ElMessage.success('配置保存成功');
    dialogVisible.value = false;
  } catch (error) {
    ElMessage.error('保存配置失败');
  } finally {
    saveLoading.value = false;
  }
};

/**
 * 测试配置
 */
const testConfig = async () => {
  try {
    testLoading.value = true;
    
    // 这里应该调用测试配置的API
    // await testLogConfig(configForm);
    
    // 模拟测试
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    ElMessage.success('配置测试通过');
  } catch (error) {
    ElMessage.error('配置测试失败');
  } finally {
    testLoading.value = false;
  }
};

/**
 * 重置表单
 */
const resetForm = () => {
  configForm.enabled = true;
  configForm.level = 'ALL';
  configForm.modules = [];
  configForm.autoClean = true;
  configForm.retentionDays = 30;
  configForm.cleanTime = '02:00';
  configForm.asyncLogging = true;
  configForm.batchSize = 100;
  configForm.cacheSize = 1000;
  configForm.sensitiveFilter = true;
  configForm.ipWhitelist = '';
  configForm.userWhitelist = [];
  formRef.value?.clearValidate();
};

/**
 * 打开对话框
 */
const open = () => {
  resetForm();
  dialogVisible.value = true;
  loadConfig();
};

// 初始化
onMounted(() => {
  // 可以在这里加载用户列表等数据
});

// 暴露方法给父组件
defineExpose({
  open
});
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
