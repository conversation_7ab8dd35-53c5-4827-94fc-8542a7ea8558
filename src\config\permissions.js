/**
 * 权限等级配置
 * 定义不同权限等级对应的按钮权限
 */

// 权限等级枚举
export const PERMISSION_LEVELS = {
  ROOT: 'root',
  ADMIN: 'admin', 
  OPERATOR: 'operator'
};

// 所有可能的按钮权限
export const ALL_PERMISSIONS = [
  'add',        // 新增
  'edit',       // 编辑
  'delete',     // 删除
  'import',     // 导入
  'export',     // 导出
  'batchAdd',   // 批量新增
  'batchDelete', // 批量删除
  'status',     // 状态切换
  'view',       // 查看详情
  'upload'      // 上传
];

// 权限等级对应的按钮权限配置
export const PERMISSION_CONFIG = {
  [PERMISSION_LEVELS.ROOT]: {
    // root权限：拥有所有权限
    permissions: ALL_PERMISSIONS,
    description: '超级管理员，拥有所有权限'
  },
  
  [PERMISSION_LEVELS.ADMIN]: {
    // admin权限：没有删除和上传权限
    permissions: ['add', 'edit', 'import', 'export', 'batchAdd', 'status', 'view'],
    description: '管理员，无删除和上传权限'
  },
  
  [PERMISSION_LEVELS.OPERATOR]: {
    // operator权限：只有查看和导出权限
    permissions: ['view', 'export'],
    description: '操作员，只能查看和导出'
  }
};

/**
 * 根据权限等级获取对应的按钮权限
 * @param {string} permissionLevel 权限等级
 * @returns {string[]} 按钮权限数组
 */
export const getPermissionsByLevel = (permissionLevel) => {
  const config = PERMISSION_CONFIG[permissionLevel];
  return config ? config.permissions : [];
};

/**
 * 检查用户是否有特定权限
 * @param {string} permissionLevel 用户权限等级
 * @param {string|string[]} requiredPermissions 需要的权限
 * @returns {boolean} 是否有权限
 */
export const hasPermission = (permissionLevel, requiredPermissions) => {
  const userPermissions = getPermissionsByLevel(permissionLevel);
  
  if (Array.isArray(requiredPermissions)) {
    // 如果是数组，需要拥有所有权限
    return requiredPermissions.every(permission => userPermissions.includes(permission));
  } else {
    // 如果是单个权限
    return userPermissions.includes(requiredPermissions);
  }
};

/**
 * 获取权限等级描述
 * @param {string} permissionLevel 权限等级
 * @returns {string} 权限描述
 */
export const getPermissionDescription = (permissionLevel) => {
  const config = PERMISSION_CONFIG[permissionLevel];
  return config ? config.description : '未知权限等级';
};
