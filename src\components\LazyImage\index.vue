<template>
  <div class="lazy-image-container" :style="{ width: width, height: height }">
    <!-- 调试信息 -->
    <div v-if="!props.src" class="image-placeholder">
      <el-icon class="placeholder-icon">
        <Picture />
      </el-icon>
      <span class="placeholder-text">暂无图片</span>
    </div>

    <!-- 直接显示图片，简化逻辑 -->
    <div v-else class="image-content">
      <img
        ref="imageRef"
        :src="optimizedSrc"
        :alt="alt"
        class="lazy-image"
        loading="lazy"
        decoding="async"
        @load="onLoad"
        @error="onError"
        @click="handlePreview"
      />

      <!-- 加载遮罩 -->
      <div v-if="loading" class="loading-overlay">
        <el-icon class="loading-icon">
          <Loading />
        </el-icon>
      </div>

      <!-- 错误状态 -->
      <div v-if="error" class="error-overlay" @click="retry">
        <el-icon class="error-icon">
          <Picture />
        </el-icon>
        <span class="error-text">加载失败，点击重试</span>
      </div>

      <!-- 预览遮罩 -->
      <div v-if="preview && !loading && !error" class="image-overlay" @click="handlePreview">
        <el-icon class="preview-icon">
          <ZoomIn />
        </el-icon>
      </div>
    </div>

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showViewer"
      :url-list="[props.src]"
      :initial-index="0"
      @close="showViewer = false"
    />
  </div>
</template>

<script setup name="LazyImage">
import { ref, onMounted, watch, computed } from 'vue';
import { Loading, Picture, ZoomIn } from '@element-plus/icons-vue';
import { generateThumbnailUrl, preloadImage } from '@/utils/imageOptimization';

const props = defineProps({
  src: {
    type: String,
    required: true
  },
  alt: {
    type: String,
    default: '图片'
  },
  width: {
    type: String,
    default: '50px'
  },
  height: {
    type: String,
    default: '50px'
  },
  preview: {
    type: Boolean,
    default: true
  },
  lazy: {
    type: Boolean,
    default: true
  },
  // 缩略图URL（用于快速预览）
  thumbnail: {
    type: String,
    default: ''
  },
  // 加载失败重试次数
  retryCount: {
    type: Number,
    default: 3
  }
});

const imageRef = ref(null);
const loading = ref(true); // 初始为加载状态
const error = ref(false);
const showViewer = ref(false);
const retryAttempts = ref(0);

// 计算优化的图片URL
const optimizedSrc = computed(() => {
  if (!props.src) return '';

  // 如果有自定义缩略图，直接使用
  if (props.thumbnail) return props.thumbnail;

  // 为小尺寸图片添加压缩参数以提升LCP
  const width = parseInt(props.width) || 100;
  const height = parseInt(props.height) || 100;

  // 只对小图片（<200px）进行压缩优化
  if (width <= 200 && height <= 200) {
    return generateThumbnailUrl(props.src, {
      width,
      height,
      quality: 85, // 稍高质量以平衡文件大小和视觉效果
      format: 'webp'
    });
  }

  // 大图片直接使用原始URL
  return props.src;
});

// 获取缩略图URL（简化版本）
const getThumbnailUrl = (url) => {
  if (props.thumbnail) return props.thumbnail;

  // 暂时直接返回原始URL进行调试
  return url;

  // 简单的缩略图处理，根据实际CDN调整
  // if (url.includes('?')) {
  //   return `${url}&w=100&h=100&q=80`;
  // } else {
  //   return `${url}?w=100&h=100&q=80`;
  // }
};

// 简化的图片加载
const loadImage = () => {
  // console.log('loadImage called, props.src:', props.src);
  if (!props.src) {
    // console.log('No src provided');
    return;
  }

  loading.value = true;
  error.value = false;

  // 使用缩略图URL
  const thumbnailUrl = getThumbnailUrl(props.src);
  // console.log('Setting currentSrc to:', thumbnailUrl);
  currentSrc.value = thumbnailUrl;

  // 设置src后，loading状态由img的onload/onerror事件控制
};

// 处理加载成功
const onLoad = () => {
  loading.value = false;
  error.value = false;

  // 预加载原图以提升后续查看体验
  if (optimizedSrc.value !== props.src && props.preview) {
    preloadImage(props.src).catch(() => {
      // 原图预加载失败不影响当前显示
    });
  }
};

// 处理加载失败
const onError = () => {
  console.log('Image load failed');
  loading.value = false;
  error.value = true;
};

// 重试加载
const retry = () => {
  console.log('Retrying image load');
  if (retryAttempts.value < props.retryCount) {
    retryAttempts.value++;
    loading.value = true;
    error.value = false;
    // 强制重新加载图片
    if (imageRef.value) {
      imageRef.value.src = props.src + '?t=' + Date.now();
    }
  }
};

// 处理预览
const handlePreview = () => {
  if (props.preview && !loading.value && !error.value) {
    showViewer.value = true;
  }
};

// 监听src变化
watch(() => props.src, () => {
  console.log('src changed to:', props.src);
  retryAttempts.value = 0;
  loading.value = true;
  error.value = false;
});

onMounted(() => {
  // console.log('LazyImage mounted with src:', props.src);
  if (props.src) {
    loading.value = true;
  }
});
</script>

<style scoped lang="scss">
.lazy-image-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
  border-radius: 4px;
  background-color: #f5f7fa;
  
  .image-placeholder,
  .image-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: #909399;
    font-size: 12px;
    cursor: default;

    .loading-icon,
    .error-icon,
    .placeholder-icon {
      font-size: 16px;
      margin-bottom: 4px;
    }

    .loading-icon {
      animation: rotate 2s linear infinite;
    }

    .placeholder-text {
      font-size: 10px;
      color: #c0c4cc;
    }
  }
  
  .image-error {
    cursor: pointer;
    transition: color 0.3s;
    
    &:hover {
      color: #409eff;
    }
  }
  
  .image-content {
    position: relative;
    width: 100%;
    height: 100%;
    contain: layout style paint; // CSS containment 优化

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1;

      .loading-icon {
        font-size: 16px;
        color: #409eff;
        animation: rotate 2s linear infinite;
      }
    }

    .error-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.9);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 2;
      cursor: pointer;

      .error-icon {
        font-size: 20px;
        color: #f56c6c;
        margin-bottom: 4px;
      }

      .error-text {
        font-size: 12px;
        color: #f56c6c;
        text-align: center;
      }
    }

    .lazy-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      cursor: pointer;
      transition: transform 0.3s ease;
      will-change: transform;
      backface-visibility: hidden;
      transform: translateZ(0); // 启用硬件加速
    }
    
    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s;
      cursor: pointer;
      
      .preview-icon {
        color: white;
        font-size: 18px;
      }
    }
    
    &:hover {
      .lazy-image {
        transform: scale(1.05);
      }
      
      .image-overlay {
        opacity: 1;
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
