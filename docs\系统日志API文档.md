# 系统日志管理API文档

本文档详细介绍了JNL-Shop应用中系统日志管理相关的API接口。

## 基础URL

所有接口的基础路径为：`/system/log`

## 响应格式

所有API响应都遵循统一的格式：

```json
{
  "code": 200,          // 状态码（200表示成功，其他值表示错误）
  "message": "操作成功", // 状态消息
  "data": { ... }       // 响应数据（根据接口而异）
}
```

## 接口列表

### 1. 分页查询操作日志

根据条件分页查询系统操作日志。

**接口地址：** `GET /system/log/page`

**请求参数：**

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Integer | 否 | 1 | 页码 |
| size | Integer | 否 | 10 | 每页大小 |
| userId | Long | 否 | - | 用户ID |
| username | String | 否 | - | 用户名（支持模糊查询） |
| module | String | 否 | - | 功能模块（支持模糊查询） |
| operation | String | 否 | - | 操作内容（支持模糊查询） |
| requestMethod | String | 否 | - | 请求方式（GET、POST等） |
| ip | String | 否 | - | 操作IP（支持模糊查询） |
| status | Integer | 否 | - | 操作状态（1成功 0失败） |
| startTime | DateTime | 否 | - | 开始时间（格式：yyyy-MM-dd HH:mm:ss） |
| endTime | DateTime | 否 | - | 结束时间（格式：yyyy-MM-dd HH:mm:ss） |
| keyword | String | 否 | - | 关键字搜索（匹配用户名、模块、操作内容） |
| orderBy | String | 否 | create_time | 排序字段 |
| orderDirection | String | 否 | desc | 排序方式（asc或desc） |

**响应示例：**

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1001,
        "userId": 10,
        "username": "admin",
        "module": "文件管理",
        "operation": "上传文件",
        "method": "com.example.pcapi.FileController.uploadFile",
        "requestUrl": "/file/upload",
        "requestMethod": "POST",
        "requestParam": "{\"fileName\":\"test.jpg\"}",
        "responseData": "{\"code\":200,\"message\":\"success\"}",
        "ip": "*************",
        "ipLocation": "内网IP",
        "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/96.0.4664.110",
        "status": 1,
        "errorMsg": null,
        "createTime": "2025-07-11 14:30:45"
      }
    ],
    "total": 150,
    "size": 10,
    "current": 1,
    "pages": 15
  }
}
```

### 2. 查询用户操作日志

根据用户ID查询该用户的操作日志。

**接口地址：** `GET /system/log/user/{userId}`

**路径参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

**查询参数：**

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| limit | Integer | 否 | 50 | 限制返回的日志数量 |

**响应示例：**

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1001,
      "userId": 10,
      "username": "admin",
      "module": "文件管理",
      "operation": "上传文件",
      "method": "com.example.pcapi.FileController.uploadFile",
      "requestUrl": "/file/upload",
      "requestMethod": "POST",
      "requestParam": "{\"fileName\":\"test.jpg\"}",
      "responseData": "{\"code\":200,\"message\":\"success\"}",
      "ip": "*************",
      "ipLocation": "内网IP",
      "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/96.0.4664.110",
      "status": 1,
      "errorMsg": null,
      "createTime": "2025-07-11 14:30:45"
    }
  ]
}
```

### 3. 查询模块操作日志

根据功能模块查询操作日志。

**接口地址：** `GET /system/log/module/{module}`

**路径参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| module | String | 是 | 功能模块名称 |

**查询参数：**

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| limit | Integer | 否 | 50 | 限制返回的日志数量 |

**响应示例：**

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1001,
      "userId": 10,
      "username": "admin",
      "module": "文件管理",
      "operation": "上传文件",
      "method": "com.example.pcapi.FileController.uploadFile",
      "requestUrl": "/file/upload",
      "requestMethod": "POST",
      "requestParam": "{\"fileName\":\"test.jpg\"}",
      "responseData": "{\"code\":200,\"message\":\"success\"}",
      "ip": "*************",
      "ipLocation": "内网IP",
      "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/96.0.4664.110",
      "status": 1,
      "errorMsg": null,
      "createTime": "2025-07-11 14:30:45"
    }
  ]
}
```

### 4. 获取操作统计信息

获取指定天数内的操作统计信息。

**接口地址：** `GET /system/log/statistics`

**查询参数：**

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| days | Integer | 否 | 7 | 统计天数 |

**响应示例：**

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalOperations": 1250,
    "successOperations": 1200,
    "failedOperations": 50,
    "successRate": 96.0,
    "moduleStats": {
      "文件管理": 450,
      "用户管理": 320,
      "订单管理": 280,
      "商品管理": 200
    },
    "dailyStats": {
      "2025-07-11": 180,
      "2025-07-10": 165,
      "2025-07-09": 190,
      "2025-07-08": 175,
      "2025-07-07": 185,
      "2025-07-06": 170,
      "2025-07-05": 185
    },
    "operationTypeStats": {
      "SELECT": 850,
      "INSERT": 150,
      "UPDATE": 200,
      "DELETE": 50
    }
  }
}
```

### 5. 清理过期日志

清理指定天数之前的操作日志。

**接口地址：** `DELETE /system/log/clean`

**查询参数：**

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| days | Integer | 否 | 30 | 保留天数（超过此天数的日志将被删除） |

**响应示例：**

```json
{
  "code": 200,
  "message": "操作成功",
  "data": 125  // 删除的日志条数
}
```

**错误响应示例：**

```json
{
  "code": 500,
  "message": "保留天数不能小于7天",
  "data": null
}
```

### 6. 查询日志详情

根据ID查询操作日志详情。

**接口地址：** `GET /system/log/{id}`

**路径参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 日志ID |

**响应示例：**

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1001,
    "userId": 10,
    "username": "admin",
    "module": "文件管理",
    "operation": "上传文件",
    "method": "com.example.pcapi.FileController.uploadFile",
    "requestUrl": "/file/upload",
    "requestMethod": "POST",
    "requestParam": "{\"fileName\":\"test.jpg\",\"fileSize\":1024,\"contentType\":\"image/jpeg\"}",
    "responseData": "{\"code\":200,\"message\":\"success\",\"data\":{\"url\":\"https://example.com/files/test.jpg\"}}",
    "ip": "*************",
    "ipLocation": "内网IP",
    "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/96.0.4664.110",
    "status": 1,
    "errorMsg": null,
    "createTime": "2025-07-11 14:30:45"
  }
}
```

**错误响应示例：**

```json
{
  "code": 500,
  "message": "日志不存在",
  "data": null
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有接口都使用`@SysLog`注解记录对系统日志本身的操作
2. 系统会自动捕获请求参数、用户信息和IP归属地
3. `status`字段使用`1`表示成功，`0`表示失败
4. 日期参数格式为`yyyy-MM-dd HH:mm:ss`
5. 日志保留的最小天数为7天
6. 所有查询操作都支持分页和排序
7. 关键字搜索支持模糊匹配用户名、模块名和操作内容
