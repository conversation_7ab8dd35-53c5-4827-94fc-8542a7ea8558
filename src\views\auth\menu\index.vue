<template>
  <div class="sku-upload-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>SKU批量上传</h2>
      <p>支持批量上传文件夹中的PNG、JPG图片文件到MinIO存储，文件名前字符必须为字母</p>
    </div>

    <!-- MinIO连接状态检查 -->
    <el-card class="status-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>MinIO连接状态</span>
          <el-button type="primary" size="small" :icon="Refresh" @click="checkMinioHealth" :loading="healthChecking">
            检查连接
          </el-button>
        </div>
      </template>
      <div class="status-content">
        <el-tag :type="minioStatus.type" size="large" :icon="minioStatus.icon">
          {{ minioStatus.text }}
        </el-tag>
        <span class="status-time" v-if="lastCheckTime">
          最后检查时间: {{ lastCheckTime }}
        </span>
      </div>
    </el-card>

    <!-- 批量上传配置 -->
    <el-card class="upload-config-card" shadow="hover">
      <template #header>
        <span>上传配置</span>
      </template>
      <el-form :model="uploadConfig" label-width="120px" class="upload-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="文件夹绝对路径" required>
              <div class="folder-selector">
                <el-input
                  v-model="uploadConfig.folderPath"
                  placeholder="请输入文件夹的完整绝对路径"
                  clearable
                  @blur="validatePath"
                >
                  <template #prepend>
                    <el-icon><Folder /></el-icon>
                  </template>
                </el-input>
                <el-button type="info" :icon="FolderOpened" @click="selectFolder" class="select-btn">
                  辅助选择
                </el-button>
              </div>
              <div class="path-examples">
                <el-text size="small" type="primary">路径格式示例：</el-text>
                <div class="example-paths">
                  <el-text size="small" type="info">Windows: C:\Users\<USER>\Desktop\图片文件夹;
                    &nbsp;&nbsp;Mac: /Users/<USER>/Desktop/图片文件夹;&nbsp;&nbsp;Linux: /home/<USER>/Desktop/图片文件夹</el-text>
                </div>
              </div>
              <div v-if="pathValidation.show" class="path-validation">
                <el-alert
                  :title="pathValidation.title"
                  :type="pathValidation.type"
                  :closable="false"
                  show-icon
                  size="small"
                >
                  {{ pathValidation.message }}
                </el-alert>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大文件大小">
              <el-input
                value="100"
                disabled
                style="width: 100%"
              >
                <template #append>MB</template>
              </el-input>
              <div class="size-note">系统限制：单个文件最大100MB</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="允许的文件类型">
              <el-select
                v-model="uploadConfig.allowedExtensions"
                multiple
                placeholder="选择允许的文件扩展名"
                style="width: 100%"
              >
                <el-option label="PNG" value="png" />
                <el-option label="JPG" value="jpg" />
                <el-option label="JPEG" value="jpeg" />
                <el-option label="GIF" value="gif" />
                <el-option label="BMP" value="bmp" />
                <el-option label="WEBP" value="webp" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上传选项">
              <div class="upload-options">
                <el-checkbox v-model="uploadConfig.overwrite">覆盖已存在文件</el-checkbox>
                <el-checkbox v-model="uploadConfig.validateExtension">验证文件扩展名</el-checkbox>
                <el-checkbox v-model="uploadConfig.async">异步上传</el-checkbox>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 上传操作区域 -->
    <el-card class="upload-action-card" shadow="hover">
      <template #header>
        <span>批量上传操作</span>
      </template>
      <div class="upload-actions">
        <el-button
          type="primary"
          size="large"
          :icon="Upload"
          @click="startBatchUpload"
          :loading="uploading"
          :disabled="!uploadConfig.folderPath || minioStatus.type !== 'success'"
        >
          {{ uploading ? '上传中...' : '开始批量上传' }}
        </el-button>
        <el-button
          size="large"
          :icon="RefreshLeft"
          @click="resetConfig"
          :disabled="uploading"
        >
          重置配置
        </el-button>
      </div>

      <!-- 上传进度 -->
      <div v-if="uploadProgress.show" class="upload-progress">
        <el-progress
          :percentage="uploadProgress.percentage"
          :status="uploadProgress.status"
          :stroke-width="8"
        />
        <div class="progress-info">
          <span>{{ uploadProgress.text }}</span>
          <span v-if="uploadResult">
            成功: {{ uploadResult.successCount }} | 失败: {{ uploadResult.failureCount }} | 总计: {{ uploadResult.totalCount }}
          </span>
        </div>
      </div>
    </el-card>

    <!-- 上传结果展示 -->
    <el-card v-if="uploadResult" class="result-card" shadow="hover">
      <template #header>
        <span>上传结果</span>
      </template>
      <div class="result-summary">
        <el-statistic title="总文件数" :value="uploadResult.totalCount" />
        <el-statistic title="成功上传" :value="uploadResult.successCount" />
        <el-statistic title="上传失败" :value="uploadResult.failureCount" />
        <el-statistic title="修改个数" :value="uploadResult.skuUpdateSuccessCount" />
        <el-statistic title="耗时" :value="uploadResult.duration" suffix="秒" />
      </div>

      <!-- 成功文件列表 -->
      <div v-if="uploadResult.successFiles && uploadResult.successFiles.length > 0" class="file-list">
        <h4>成功上传的文件:</h4>
        <el-table :data="uploadResult.successFiles" size="small" max-height="200">
          <el-table-column prop="fileName" label="文件名" />
          <el-table-column prop="filePath" label="存储路径" />
          <el-table-column prop="fileSize" label="文件大小" />
        </el-table>
      </div>

      <!-- 失败文件列表 -->
      <div v-if="uploadResult.failureFiles && uploadResult.failureFiles.length > 0" class="file-list">
        <h4>上传失败的文件:</h4>
        <el-table :data="uploadResult.failureFiles" size="small" max-height="200">
          <el-table-column prop="fileName" label="文件名" />
          <el-table-column prop="errorMessage" label="失败原因" />
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup name="skuUpload">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Upload,
  Refresh,
  RefreshLeft,
  Folder,
  FolderOpened,
  SuccessFilled,
  CircleCloseFilled,
  WarningFilled
} from "@element-plus/icons-vue";
import { minioHealthCheck, batchUploadFromFolder } from "@/api/modules/upload";

// MinIO连接状态
const minioStatus = ref({
  type: "info",
  text: "未检查",
  icon: WarningFilled
});
const healthChecking = ref(false);
const lastCheckTime = ref("");

// 上传配置
const uploadConfig = reactive({
  folderPath: "",
  overwrite: false,
  validateExtension: true,
  allowedExtensions: ["png", "jpg", "jpeg"],
  maxFileSizeMb: 100, // 固定为100MB，用户不可更改
  async: false
});

// 上传状态
const uploading = ref(false);
const uploadProgress = reactive({
  show: false,
  percentage: 0,
  status: "",
  text: ""
});

// 上传结果
const uploadResult = ref(null);

// 选择的文件列表
const selectedFiles = ref([]);

// 路径验证
const pathValidation = reactive({
  show: false,
  type: 'info',
  title: '',
  message: ''
});

// 检查MinIO健康状态
const checkMinioHealth = async () => {
  try {
    healthChecking.value = true;
    const response = await minioHealthCheck();

    if (response && response.code === 200) {
      minioStatus.value = {
        type: "success",
        text: "连接正常",
        icon: SuccessFilled
      };
      ElMessage.success("MinIO连接正常");
    } else {
      throw new Error(response?.message || "连接失败");
    }
  } catch (error) {
    console.error("MinIO健康检查失败:", error);
    minioStatus.value = {
      type: "danger",
      text: "连接失败",
      icon: CircleCloseFilled
    };
    ElMessage.error("MinIO连接失败: " + (error.message || "未知错误"));
  } finally {
    healthChecking.value = false;
    lastCheckTime.value = new Date().toLocaleString();
  }
};

// 辅助选择文件夹（仅用于获取文件夹名称）
const selectFolder = () => {
  ElMessageBox.alert(
    '由于浏览器安全限制，无法直接获取文件夹的绝对路径。请按以下步骤操作：\n\n' +
    '1. 在文件管理器中找到要上传的文件夹\n' +
    '2. 右键点击文件夹，选择"属性"或"显示简介"\n' +
    '3. 复制完整的文件夹路径\n' +
    '4. 将路径粘贴到上方的输入框中\n\n' +
    '或者点击"继续选择"来获取文件夹名称作为参考',
    '路径获取说明',
    {
      confirmButtonText: '继续选择',
      cancelButtonText: '我知道了',
      showCancelButton: true,
      type: 'info'
    }
  ).then(() => {
    // 用户选择继续，打开文件夹选择对话框
    const input = document.createElement('input');
    input.type = 'file';
    input.webkitdirectory = true;
    input.multiple = true;
    input.accept = 'image/*';

    input.onchange = (event) => {
      const files = Array.from(event.target.files);
      if (files.length > 0) {
        selectedFiles.value = files;

        const firstFile = files[0];
        const pathParts = firstFile.webkitRelativePath.split('/');
        const folderName = pathParts[0];

        // 显示文件统计信息
        const imageFiles = files.filter(file => {
          const ext = file.name.split('.').pop().toLowerCase();
          return ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'].includes(ext);
        });

        ElMessage.success(`检测到文件夹名称: "${folderName}"，包含 ${imageFiles.length} 个图片文件`);

        // 提示用户输入完整路径
        ElMessageBox.prompt(
          `检测到文件夹名称为: ${folderName}\n\n请输入该文件夹的完整绝对路径:`,
          '输入完整路径',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputValue: uploadConfig.folderPath || '',
            inputPlaceholder: '例如: C:\\Users\\<USER>\\Desktop\\' + folderName
          }
        ).then(({ value }) => {
          if (value && value.trim()) {
            uploadConfig.folderPath = value.trim();
            validatePath();
          }
        }).catch(() => {
          // 用户取消输入
        });
      }
    };

    input.click();
  }).catch(() => {
    // 用户点击"我知道了"
  });
};

// 验证路径格式
const validatePath = () => {
  const path = uploadConfig.folderPath.trim();

  if (!path) {
    pathValidation.show = false;
    return;
  }

  // 检查是否为绝对路径
  const isWindows = /^[A-Za-z]:\\/.test(path); // Windows: C:\
  const isUnix = /^\//.test(path); // Unix/Linux/Mac: /

  if (isWindows || isUnix) {
    pathValidation.show = true;
    pathValidation.type = 'success';
    pathValidation.title = '路径格式正确';
  } else {
    pathValidation.show = true;
    pathValidation.type = 'warning';
    pathValidation.title = '路径格式提醒';
    pathValidation.message = '请确保输入的是完整的绝对路径，以便后端能够正确访问文件夹';
  }
};

// 开始批量上传
const startBatchUpload = async () => {
  // 验证配置
  if (!uploadConfig.folderPath.trim()) {
    ElMessage.warning("请输入文件夹的绝对路径");
    return;
  }

  // 验证路径格式
  const path = uploadConfig.folderPath.trim();
  const isWindows = /^[A-Za-z]:\\/.test(path);
  const isUnix = /^\//.test(path);

  if (!isWindows && !isUnix) {
    ElMessage.warning("请输入有效的绝对路径格式");
    return;
  }

  if (minioStatus.value.type !== "success") {
    ElMessage.warning("请先检查MinIO连接状态");
    return;
  }

  try {
    // 确认上传
    await ElMessageBox.confirm(
      `确认要批量上传以下路径中的图片文件吗？\n\n路径: ${uploadConfig.folderPath}\n\n后端将扫描该路径下的所有图片文件进行上传。`,
      "确认上传",
      {
        type: "warning",
        confirmButtonText: "确认上传",
        cancelButtonText: "取消"
      }
    );

    uploading.value = true;
    uploadProgress.show = true;
    uploadProgress.percentage = 0;
    uploadProgress.status = "active";
    uploadProgress.text = "准备上传...";
    uploadResult.value = null;

    // 构建请求参数
    const requestData = {
      folderPath: uploadConfig.folderPath.trim(),
      overwrite: uploadConfig.overwrite,
      validateExtension: uploadConfig.validateExtension,
      allowedExtensions: uploadConfig.allowedExtensions,
      maxFileSizeMb: uploadConfig.maxFileSizeMb,
      async: uploadConfig.async
    };

    console.log("开始批量上传，请求参数:", requestData);

    // 模拟上传进度（因为是批量上传，无法获取实时进度）
    const progressInterval = setInterval(() => {
      if (uploadProgress.percentage < 90) {
        uploadProgress.percentage += Math.random() * 10;
        uploadProgress.text = `上传中... ${Math.round(uploadProgress.percentage)}%`;
      }
    }, 500);

    const startTime = Date.now();

    // 调用批量上传API
    const response = await batchUploadFromFolder(requestData);

    clearInterval(progressInterval);

    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);

    console.log("批量上传响应:", response);

    if (response && response.code === 200) {
      uploadProgress.percentage = 100;
      uploadProgress.status = "success";
      uploadProgress.text = "上传完成";

      // 处理上传结果
      const resultData = response.data || {};
      uploadResult.value = {
        totalCount: resultData.totalCount || 0,
        successCount: resultData.successCount || 0,
        failureCount: resultData.failureCount || 0,
        duration: duration,
        successFiles: resultData.successFiles || [],
        failureFiles: resultData.failureFiles || []
      };

      ElMessage.success(`批量上传完成！成功: ${uploadResult.value.successCount}, 失败: ${uploadResult.value.failureCount}`);
    } else {
      throw new Error(response?.message || "上传失败");
    }

  } catch (error) {
    console.error("批量上传失败:", error);
    uploadProgress.status = "exception";
    uploadProgress.text = "上传失败";

    if (error !== "cancel") {
      ElMessage.error("批量上传失败: " + (error.message || "未知错误"));
    }
  } finally {
    uploading.value = false;
  }
};

// 重置配置
const resetConfig = () => {
  Object.assign(uploadConfig, {
    folderPath: "",
    overwrite: false,
    validateExtension: true,
    allowedExtensions: ["png", "jpg", "jpeg"],
    maxFileSizeMb: 100,
    async: false
  });

  selectedFiles.value = [];
  uploadProgress.show = false;
  uploadProgress.percentage = 0;
  uploadProgress.status = "";
  uploadProgress.text = "";
  uploadResult.value = null;

  ElMessage.success("配置已重置");
};

// 组件挂载时检查MinIO连接
onMounted(() => {
  checkMinioHealth();
});
</script>

<style scoped lang="scss">
.sku-upload-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .page-header {
    margin-bottom: 24px;
    text-align: center;

    h2 {
      color: #303133;
      margin-bottom: 8px;
      font-size: 24px;
      font-weight: 600;
    }

    p {
      color: #606266;
      font-size: 14px;
      line-height: 1.5;
      margin: 0;
    }
  }

  .status-card,
  .upload-config-card,
  .upload-action-card,
  .result-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }

  .status-content {
    display: flex;
    align-items: center;
    gap: 16px;

    .status-time {
      color: #909399;
      font-size: 12px;
    }
  }

  .upload-form {
    .folder-selector {
      display: flex;
      gap: 12px;
      align-items: flex-end;
      justify-content: center;

      .el-input {
        flex: 1;
      }

      .select-btn {
        flex-shrink: 0;
        // height: 32px;
      }
    }

    .size-note {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }

    .path-examples {
      margin-top: 8px;
      padding: 8px;
      background-color: #f8f9fa;
      border-radius: 4px;

      .example-paths {
        display: flex;
        flex-direction: column;
        gap: 4px;
        margin-top: 4px;

        
      }
    }

    .path-validation {
      margin-top: 8px;
    }

    .upload-options {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
  }

  .upload-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin-bottom: 20px;
  }

  .upload-progress {
    margin-top: 20px;

    .progress-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 8px;
      font-size: 14px;
      color: #606266;
    }
  }

  .result-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
  }

  .file-list {
    margin-top: 20px;

    h4 {
      color: #303133;
      margin-bottom: 12px;
      font-size: 16px;
      font-weight: 600;
    }
  }
}

// 卡片样式优化
:deep(.el-card) {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s;

  &:hover {
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  }

  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;
  }

  .el-card__body {
    padding: 20px;
  }
}

// 表单样式优化
:deep(.el-form) {
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }

  .el-input-number {
    width: 100%;
  }
}

// 进度条样式
:deep(.el-progress) {
  .el-progress__text {
    font-weight: 600;
  }
}

// 统计数字样式
:deep(.el-statistic) {
  text-align: center;

  .el-statistic__head {
    color: #909399;
    font-size: 14px;
    margin-bottom: 8px;
  }

  .el-statistic__content {
    color: #303133;
    font-size: 24px;
    font-weight: 600;
  }
}

// 表格样式
:deep(.el-table) {
  .el-table__header {
    background-color: #fafafa;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .sku-upload-container {
    padding: 16px;

    .upload-actions {
      flex-direction: column;
      align-items: center;

      .el-button {
        width: 100%;
        max-width: 300px;
      }
    }

    .result-summary {
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }

    .upload-form {
      .el-row {
        .el-col {
          margin-bottom: 16px;
        }
      }

      .folder-selector {
        flex-direction: column;
        gap: 8px;

        .select-btn {
          width: 100%;
          height: 40px;
        }
      }

      .upload-options {
        .el-checkbox {
          margin-bottom: 8px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .sku-upload-container {
    .result-summary {
      grid-template-columns: 1fr;
    }

    .status-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .progress-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }
  }
}
</style>
