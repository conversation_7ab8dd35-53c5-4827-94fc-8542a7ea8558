import { defineStore } from "pinia";
import { getAuthButtonListApi, getAuthMenuListApi } from "@/api/modules/login";
import { getFlatMenuList, getShowMenuList, getAllBreadcrumbList } from "@/utils";
import { getPermissionsByLevel } from "@/config/permissions";

export const useAuthStore = defineStore({
  id: "wujin-auth",
  state: () => ({
    // 用户权限等级 (root, admin, operator)
    permissionLevel: "",
    // 按钮权限列表 (基于权限等级生成)
    authButtonList: {},
    // 菜单权限列表
    authMenuList: [],
    // 当前页面的 router name，用来做按钮权限筛选
    routeName: ""
  }),
  getters: {
    // 用户权限等级
    permissionLevelGet: state => state.permissionLevel,
    // 按钮权限列表 (基于权限等级动态生成)
    authButtonListGet: state => {
      if (!state.permissionLevel) return {};
      const permissions = getPermissionsByLevel(state.permissionLevel);
      // 为所有页面生成相同的权限配置
      return {
        useProTable: permissions,
        authButton: permissions,
        userManagement: permissions,
        roleManagement: permissions,
        // 可以根据需要添加更多页面的权限配置
      };
    },
    // 菜单权限列表 ==> 这里的菜单没有经过任何处理
    authMenuListGet: state => state.authMenuList,
    // 菜单权限列表 ==> 左侧菜单栏渲染，需要剔除 isHide == true
    showMenuListGet: state => getShowMenuList(state.authMenuList),
    // 菜单权限列表 ==> 扁平化之后的一维数组菜单，主要用来添加动态路由
    flatMenuListGet: state => getFlatMenuList(state.authMenuList),
    // 递归处理后的所有面包屑导航列表
    breadcrumbListGet: state => getAllBreadcrumbList(state.authMenuList)
  },
  actions: {
    // Get AuthButtonList (获取用户权限等级)
    async getAuthButtonList() {
      const { data } = await getAuthButtonListApi();
      // 设置用户权限等级
      this.permissionLevel = data.permission || "";
    },
    // Get AuthMenuList
    async getAuthMenuList() {
      const { data } = await getAuthMenuListApi();
      this.authMenuList = data;
    },
    // Set RouteName
    async setRouteName(name) {
      this.routeName = name;
    },
    // Set Permission Level (手动设置权限等级)
    setPermissionLevel(level) {
      this.permissionLevel = level;
    }
  }
});
