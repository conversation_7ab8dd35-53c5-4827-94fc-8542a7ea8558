<template>
  <div class="upload content-box">
    <!-- oss - sku上传 -->
    <div class="card img-box">
      <span class="text">oss - sku上传说明 🍓🍇🍈🍉</span>
      <el-descriptions title="上传说明 📚" :column="1" border>
        <el-descriptions-item label="oss标头">
          为该编码的字母表述，比如AAA,AAB,CUABA
        </el-descriptions-item>
        <el-descriptions-item label="oss实际储存路径">
          要求在创建oss实际储存路径时，精确细分，如 /sku/A/AA/AAA、/sku/C/CU/CUA/CUAB/CUABA  &nbsp;&nbsp;与之类似
        </el-descriptions-item>
        <el-descriptions-item label="oss拼接规则"> 聚耐力服务器域名地址+标头+编码(sku文件名)， 如 https://junaili.com:9000/sku/A/AA/AAA/AAA-M10×100-20 </el-descriptions-item>
        <el-descriptions-item label="文件准备"> 准备的SKU上传文件中，所有的图片资源保持文件格式为JPG，PNG等常规的图片格式，切每张sku的文件名必须跟该sku的商品的商品编码保持一致 </el-descriptions-item>
        <el-descriptions-item label="上传准备"> 上传时，确保该编码类型，如AAA，ABA，ACA的地址在OSS中存在，如不存在，及时新建，如该编码类型的商品信息未上传，上传依旧成功 </el-descriptions-item>
        <el-descriptions-item label="fileType">
          图片类型限制，默认类型为 ["image/jpeg", "image/png", "image/gif"]
        </el-descriptions-item>
        <el-descriptions-item label="height"> 组件高度样式，默认为 "150px" </el-descriptions-item>
        <el-descriptions-item label="width"> 组件宽度样式，默认为 "150px" </el-descriptions-item>
        <el-descriptions-item label="borderRadius"> 组件边框圆角样式，默认为 "8px" </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script setup name="uploadFile">
import { ref, reactive } from "vue";

const ruleFormRef = ref();
const submit = () => {
  ruleFormRef.value.validate(valid => {
    console.log(valid);
  });
};
</script>

<style scoped lang="scss">
@import "./index";
</style>
