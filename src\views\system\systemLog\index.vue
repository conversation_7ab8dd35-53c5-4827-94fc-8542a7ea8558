<template>
  <div class="main-box">
    <div class="table-box">
      <ProTable
        ref="proTable"
        title="系统日志"
        :columns="columns"
        :request-api="getSystemLogPage"
        :request-auto="true"
        :init-param="initParam"
        :data-callback="dataCallback"
        :pagination="true"
        :page-size="50"
        :show-overflow-tooltip="true"
      >
        <!-- 表格 header 按钮 -->
        <template #tableHeader="scope">
          <el-button v-auth="'export'" type="primary" :icon="Download" plain @click="exportLogs">导出日志</el-button>
          <el-button v-auth="'statistics'" type="primary" :icon="TrendCharts" plain @click="openStatistics">日志统计</el-button>
          <el-button v-auth="'analysis'" type="success" :icon="DataAnalysis" plain @click="openAnalysis">日志分析</el-button>
          <el-button v-auth="'config'" type="info" :icon="Setting" plain @click="openConfig">日志配置</el-button>
          <el-button v-auth="'clean'" type="warning" :icon="Delete" plain @click="cleanLogs">清理日志</el-button>
          <el-button type="danger" :icon="Delete" plain :disabled="!scope.isSelected" @click="batchDeleteLogs(scope.selectedListIds)">
            批量删除
          </el-button>
        </template>

        <!-- 状态渲染 -->
        <template #status="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? '成功' : '失败' }}
          </el-tag>
        </template>

        <!-- 请求方法渲染 -->
        <template #requestMethod="scope">
          <el-tag
            :type="getMethodTagType(scope.row.requestMethod)"
            size="small"
          >
            {{ scope.row.requestMethod }}
          </el-tag>
        </template>

        <!-- IP地址渲染 -->
        <template #ip="scope">
          <div>
            <div>{{ scope.row.ip }}</div>
            <div class="text-xs text-gray-500">{{ scope.row.ipLocation }}</div>
          </div>
        </template>

        <!-- 操作时间渲染 -->
        <template #createTime="scope">
          <div>{{ formatDateTime(scope.row.createTime) }}</div>
        </template>

        <!-- 表格操作 -->
        <template #created="scope">
          <el-button type="primary" link :icon="View" @click="viewLogDetail(scope.row)">详情</el-button>
          <el-button v-if="scope.row.userId" type="primary" link :icon="User" @click="viewUserLogs(scope.row.userId)">用户日志</el-button>
          <el-button type="danger" link :icon="Delete" @click="deleteLog(scope.row)">删除</el-button>
        </template>
      </ProTable>

      <!-- 日志详情弹窗 -->
      <LogDetailDialog ref="logDetailRef" />

      <!-- 日志统计弹窗 -->
      <LogStatisticsDialog ref="logStatisticsRef" />

      <!-- 日志分析弹窗 -->
      <LogAnalysisDialog ref="logAnalysisRef" />

      <!-- 日志配置弹窗 -->
      <LogConfigDialog ref="logConfigRef" />

      <!-- 清理日志弹窗 -->
      <LogCleanDialog ref="logCleanRef" @refresh="refreshTable" />

      <!-- 导出日志弹窗 -->
      <LogExportDialog ref="logExportRef" />
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Download, Delete, View, User, TrendCharts, DataAnalysis, Setting } from "@element-plus/icons-vue";
import ProTable from "@/components/ProTable/index.vue";
import LogDetailDialog from "./components/LogDetailDialog.vue";
import LogStatisticsDialog from "./components/LogStatisticsDialog.vue";
import LogAnalysisDialog from "./components/LogAnalysisDialog.vue";
import LogConfigDialog from "./components/LogConfigDialog.vue";
import LogCleanDialog from "./components/LogCleanDialog.vue";
import LogExportDialog from "./components/LogExportDialog.vue";
import {
  getSystemLogPage,
  getSystemLogDetail,
  batchDeleteSystemLog,
  exportSystemLog
} from "@/api/modules/systemLog";
import { SystemLogStatus, SystemLogRequestMethods } from "@/api/interface/systemLog";
import { formatDateTime } from "@/utils/date";

// ProTable 实例
const proTable = ref();

// 弹窗实例
const logDetailRef = ref();
const logStatisticsRef = ref();
const logAnalysisRef = ref();
const logConfigRef = ref();
const logCleanRef = ref();
const logExportRef = ref();

// 初始化参数
const initParam = reactive({});

// 数据回调处理
const dataCallback = (data) => {
  console.log('系统日志数据回调:', data);
  // 处理后端返回的数据格式 {code: 200, message: "操作成功", data: {total: xxx, records: [...]}}
  if (data && data.data) {
    const records = data.data.records || [];
    return {
      list: records,
      total: data.data.total || 0
    };
  }
  // 兼容其他可能的数据格式
  return {
    list: data.records || data.list || [],
    total: data.total || 0
  };
};

// 表格配置项
const columns = reactive([
  { type: "selection", fixed: "left", width: 70 },
  {
    prop: "id",
    label: "日志ID",
    width: 100
  },
  {
    prop: "username",
    label: "用户名",
    width: 120,
    search: { el: "input", tooltip: "请输入用户名", key: "username" }
  },
  {
    prop: "module",
    label: "功能模块",
    width: 150,
    search: { el: "input", tooltip: "请输入功能模块", key: "module" }
  },
  {
    prop: "operation",
    label: "操作内容",
    width: 250,
    search: { el: "input", tooltip: "请输入操作内容", key: "operation" }
  },
  {
    prop: "requestMethod",
    label: "请求方式",
    width: 100,
    search: {
      el: "select",
      props: {
        filterable: true,
        clearable: true,
        placeholder: "请选择请求方式"
      },
      options: [
        { label: "GET", value: "GET" },
        { label: "POST", value: "POST" },
        { label: "PUT", value: "PUT" },
        { label: "DELETE", value: "DELETE" },
        { label: "PATCH", value: "PATCH" }
      ]
    }
  },
  {
    prop: "ip",
    label: "操作IP",
    width: 150,
    search: { el: "input", tooltip: "请输入IP地址", key: "ip" }
  },
  {
    prop: "status",
    label: "操作状态",
    width: 100,
    search: {
      el: "select",
      props: {
        filterable: true,
        clearable: true,
        placeholder: "请选择状态"
      },
      options: [
        { label: "成功", value: 1 },
        { label: "失败", value: 0 }
      ]
    }
  },
  {
    prop: "createTime",
    label: "操作时间",
    width: 180,
    search: {
      el: "date-picker",
      span: 2,
      props: {
        type: "datetimerange",
        valueFormat: "YYYY-MM-DD HH:mm:ss",
        startPlaceholder: "开始时间",
        endPlaceholder: "结束时间"
      },
      key: "timeRange"
    }
  },
  {
    prop: "keyword",
    label: "关键字搜索",
    isShow: false,
    search: {
      el: "input",
      tooltip: "搜索用户名、模块、操作内容",
      key: "keyword",
      span: 2
    }
  },
  { prop: "created", label: "操作", fixed: "right", width: 250 }
]);

// ==================== 工具方法 ====================

/**
 * 获取请求方法标签类型
 */
const getMethodTagType = (method) => {
  const typeMap = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger',
    'PATCH': 'info'
  };
  return typeMap[method] || 'info';
};

/**
 * 刷新表格
 */
const refreshTable = () => {
  proTable.value?.getTableList();
  ElMessage.success("数据已刷新");
};

// ==================== 日志操作方法 ====================

/**
 * 查看日志详情
 */
const viewLogDetail = async (row) => {
  try {
    const { data } = await getSystemLogDetail(row.id);
    logDetailRef.value?.open(data);
  } catch (error) {
    ElMessage.error("获取日志详情失败");
  }
};

/**
 * 查看用户日志
 */
const viewUserLogs = (userId) => {
  // 设置用户ID筛选条件并刷新表格
  proTable.value.searchParam.userId = userId;
  proTable.value.search();
  ElMessage.success(`已筛选用户ID为 ${userId} 的日志`);
};

/**
 * 删除单个日志
 */
const deleteLog = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除日志ID为 ${row.id} 的记录吗？`,
      "删除确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    await batchDeleteSystemLog([row.id]);
    ElMessage.success("删除成功");
    refreshTable();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error("删除失败");
    }
  }
};

/**
 * 批量删除日志
 */
const batchDeleteLogs = async (ids) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${ids.length} 条日志记录吗？`,
      "批量删除确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    await batchDeleteSystemLog(ids);
    ElMessage.success("批量删除成功");
    refreshTable();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error("批量删除失败");
    }
  }
};

// ==================== 功能操作方法 ====================

/**
 * 导出日志
 */
const exportLogs = () => {
  logExportRef.value?.open();
};

/**
 * 打开日志统计
 */
const openStatistics = () => {
  logStatisticsRef.value?.open();
};

/**
 * 打开日志分析
 */
const openAnalysis = () => {
  logAnalysisRef.value?.open();
};

/**
 * 打开日志配置
 */
const openConfig = () => {
  logConfigRef.value?.open();
};

/**
 * 清理日志
 */
const cleanLogs = () => {
  logCleanRef.value?.open();
};

// ==================== 生命周期 ====================

onMounted(() => {
  // 组件挂载后的初始化操作
});
</script>

<style scoped>
.main-box {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.table-box {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.text-xs {
  font-size: 0.75rem;
}

.text-gray-500 {
  color: #6b7280;
}
</style>
