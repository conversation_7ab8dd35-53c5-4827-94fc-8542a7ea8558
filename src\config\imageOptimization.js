/**
 * 图片优化配置
 * 针对生产环境的图片加载优化配置
 */

// 生产环境图片优化配置
export const PRODUCTION_IMAGE_CONFIG = {
  // 懒加载配置
  lazyLoad: {
    enabled: true,
    rootMargin: '100px', // 提前100px开始加载
    threshold: 0.1,
    retryCount: 3,
    retryDelay: 1000 // 重试延迟
  },
  
  // 缓存配置
  cache: {
    enabled: true,
    maxAge: 30 * 60 * 1000, // 30分钟
    maxSize: 100, // 最大缓存100张图片
    cleanupInterval: 10 * 60 * 1000 // 每10分钟清理一次
  },
  
  // 缩略图配置
  thumbnail: {
    enabled: true,
    defaultWidth: 100,
    defaultHeight: 100,
    quality: 80,
    format: 'webp', // 优先使用webp格式
    fallbackFormat: 'jpg'
  },
  
  // 预加载配置
  preload: {
    enabled: true,
    concurrency: 3, // 并发加载数量
    priorityImages: 5, // 优先加载前5张图片
    backgroundPreload: true // 后台预加载
  },
  
  // 错误处理配置
  errorHandling: {
    showPlaceholder: true,
    enableRetry: true,
    maxRetries: 3,
    retryDelay: 1000,
    fallbackImage: '/images/placeholder.png'
  },
  
  // 性能监控配置
  performance: {
    enabled: process.env.NODE_ENV === 'development',
    reportInterval: 30000, // 30秒报告一次
    trackCacheHits: true,
    trackLoadTimes: true
  }
};

// CDN配置映射
export const CDN_CONFIGS = {
  // 阿里云OSS
  aliyun: {
    domains: ['aliyuncs.com'],
    thumbnailParams: (width, height, quality, format) => 
      `?x-oss-process=image/resize,w_${width},h_${height}/quality,q_${quality}/format,${format}`,
    webpSupport: true
  },
  
  // 腾讯云COS
  tencent: {
    domains: ['myqcloud.com'],
    thumbnailParams: (width, height, quality, format) => 
      `?imageView2/1/w/${width}/h/${height}/q/${quality}/format/${format}`,
    webpSupport: true
  },
  
  // 七牛云
  qiniu: {
    domains: ['qiniudn.com', 'clouddn.com'],
    thumbnailParams: (width, height, quality, format) => 
      `?imageView2/1/w/${width}/h/${height}/q/${quality}/format/${format}`,
    webpSupport: true
  },
  
  // 通用CDN
  generic: {
    domains: [],
    thumbnailParams: (width, height, quality, format) => 
      `?w=${width}&h=${height}&q=${quality}&f=${format}`,
    webpSupport: false
  }
};

// 检测CDN类型
export function detectCDNType(url) {
  for (const [type, config] of Object.entries(CDN_CONFIGS)) {
    if (config.domains.some(domain => url.includes(domain))) {
      return type;
    }
  }
  return 'generic';
}

// 检测浏览器WebP支持
export function checkWebPSupport() {
  return new Promise((resolve) => {
    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
}

// 获取优化后的图片URL
export function getOptimizedImageUrl(originalUrl, options = {}) {
  if (!originalUrl) return '';
  
  const config = PRODUCTION_IMAGE_CONFIG.thumbnail;
  const {
    width = config.defaultWidth,
    height = config.defaultHeight,
    quality = config.quality,
    format = config.format
  } = options;
  
  const cdnType = detectCDNType(originalUrl);
  const cdnConfig = CDN_CONFIGS[cdnType];
  
  // 如果CDN不支持WebP或浏览器不支持WebP，使用fallback格式
  const finalFormat = (cdnConfig.webpSupport && window.webpSupported) ? format : config.fallbackFormat;
  
  return originalUrl + cdnConfig.thumbnailParams(width, height, quality, finalFormat);
}

// 初始化图片优化
export async function initImageOptimization() {
  // 检测WebP支持
  window.webpSupported = await checkWebPSupport();
  
  // 设置全局配置
  window.imageOptimizationConfig = PRODUCTION_IMAGE_CONFIG;
  
  console.log('图片优化初始化完成', {
    webpSupported: window.webpSupported,
    config: PRODUCTION_IMAGE_CONFIG
  });
}

// 图片加载优先级队列
export class ImageLoadQueue {
  constructor(concurrency = 3) {
    this.concurrency = concurrency;
    this.queue = [];
    this.running = [];
  }
  
  add(imageUrl, priority = 0) {
    return new Promise((resolve, reject) => {
      this.queue.push({
        url: imageUrl,
        priority,
        resolve,
        reject
      });
      
      // 按优先级排序
      this.queue.sort((a, b) => b.priority - a.priority);
      
      this.process();
    });
  }
  
  async process() {
    if (this.running.length >= this.concurrency || this.queue.length === 0) {
      return;
    }
    
    const task = this.queue.shift();
    this.running.push(task);
    
    try {
      const img = new Image();
      img.onload = () => {
        task.resolve(task.url);
        this.complete(task);
      };
      img.onerror = () => {
        task.reject(new Error(`Failed to load: ${task.url}`));
        this.complete(task);
      };
      img.src = task.url;
    } catch (error) {
      task.reject(error);
      this.complete(task);
    }
  }
  
  complete(task) {
    const index = this.running.indexOf(task);
    if (index > -1) {
      this.running.splice(index, 1);
    }
    
    // 继续处理队列
    this.process();
  }
  
  clear() {
    this.queue = [];
    this.running = [];
  }
}

// 创建全局图片加载队列
export const globalImageQueue = new ImageLoadQueue(PRODUCTION_IMAGE_CONFIG.preload.concurrency);

// 表格图片优化配置
export const TABLE_IMAGE_CONFIG = {
  // 表格中的图片尺寸
  tableImageSize: {
    width: 50,
    height: 50,
    quality: 70
  },
  
  // 预览图片尺寸
  previewImageSize: {
    width: 800,
    height: 600,
    quality: 90
  },
  
  // 批量加载配置
  batchLoad: {
    enabled: true,
    batchSize: 10, // 每批加载10张图片
    delay: 100 // 批次间延迟100ms
  }
};
