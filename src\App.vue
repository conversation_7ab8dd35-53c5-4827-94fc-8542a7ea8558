<template>
  <el-config-provider :locale="locale" :size="assemblySize" :button="buttonConfig">
    <router-view></router-view>
  </el-config-provider>
</template>

<script setup>
import { onMounted, reactive, computed } from "vue";
import { useI18n } from "vue-i18n";
import { getBrowserLang } from "@/utils";
import { useTheme } from "@/hooks/useTheme";
import { ElConfigProvider } from "element-plus";
import { useGlobalStore } from "@/stores/modules/global";
import en from "element-plus/es/locale/lang/en";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import { test } from "@/api/modules/user";
import { loginApi } from "@/api/modules/login";

const globalStore = useGlobalStore();

// init theme
const { initTheme } = useTheme();
initTheme();

async function testApi() {
  try {
    console.log('开始测试API...');
    const res = await test();
    console.log('API测试成功:', res);
  } catch (error) {
    console.error('API测试失败:', error);
  }
}

// 测试登录API
async function testLogin() {
  try {
    console.log('开始测试登录API...');
    const loginData = {
      username: "<EMAIL>",
      password: "password123",
      rememberMe: false
    };
    const res = await loginApi(loginData);
    console.log('登录API测试成功:', res);
  } catch (error) {
    console.error('登录API测试失败:', error);
  }
}

// init language
const i18n = useI18n();
onMounted(() => {
  const language = globalStore.language ?? getBrowserLang();
  i18n.locale.value = language;
  globalStore.setGlobalState("language", language);
  testApi();
});

// element language
const locale = computed(() => {
  if (globalStore.language === "zh") return zhCn;
  if (globalStore.language === "en") return en;
  return getBrowserLang() === "zh" ? zhCn : en;
});

// element assemblySize
const assemblySize = computed(() => globalStore.assemblySize);

// element button config
const buttonConfig = reactive({ autoInsertSpace: false });
</script>
