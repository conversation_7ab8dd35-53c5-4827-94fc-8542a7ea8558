# Changelog

## [1.0](https://github.com/lw123lw/Geeker-Admin-JS/releases/tag/V1.0) (2024-08-17)

### Features

- 🚀 新增树型 ProTbale 图谱功能及示例(更多查看详情) ([57536df](https://gitee.com/haimashale/geeker-admin-js/tree/00c0d76ed2c6008cf0a7f5b4ba75e69c565c64c3))
- 🚀 新增图谱鼠标悬浮在节点上直接关联节点高亮(查看详情) ([2cef99d](https://gitee.com/haimashale/geeker-admin-js/commit/2cef99d3862ffd7503fab1cdc40feb1d41e80321))
- 🚀 新增图谱可配置项，包含自定义节点形状、大小、是否可拖拽节点、连线类型、连线颜色等...([bf7ef1f](https://gitee.com/haimashale/geeker-admin-js/commit/bf7ef1f9a4ef080f15fd64d780b1b7658447c1e1))
- 🚀 新增图谱可配置项layouts,允许配置节点之间的互斥力大小和连线的拉力大小([c1a5b09](https://gitee.com/haimashale/geeker-admin-js/commit/c1a5b09456d9f6d681e0a228c1836f134d5572db))

### Fix Bug

- 🧩 修复图谱点击A节点后再右键B节点，图谱不会聚焦B节点的bug([a0dbd70](https://gitee.com/haimashale/geeker-admin-js/tree/a0dbd70f52800c6a7bd55e055d573af195a1b814/))

### Refactor Code

- ♻️ 分离图谱函数为hooks([749bd58](https://gitee.com/haimashale/geeker-admin-js/commit/749bd58a7ed4bb2435f9c35a797461e78e2caa44))
