import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

// 分页获取商品列表
export const getProductList = params => {
  return http.get(PORT1 + `/product/page`, params);
};

// 根据商品编码搜索商品
export const getProductByCode = params => {
  return http.get(PORT1 + `/product/code`, params);
};

// 根据品牌搜索商品
export const getProductByBrand = params => {
  return http.get(PORT1 + `/product/brand`, params);
};

// 根据商品名称搜索商品
export const getProductByName = params => {
  return http.get(PORT1 + `/product/name`, params);
};

// 根据分类获取商品
export const getProductByCategory = (fid, params) => {
  return http.get(PORT1 + `/product/category/${fid}`, params);
};

// 新增商品 (预留接口)
export const addProduct = params => {
  return http.post(PORT1 + `/product/add`, params);
};

// 编辑商品 (预留接口)
export const editProduct = params => {
  return http.post(PORT1 + `/product/update`, params);
};

// 删除商品 (预留接口)
export const deleteProduct = id => {
  return http.get(PORT1 + `/product/delete/${id}`);
};

// 批量删除商品 (预留接口)
export const batchDeleteProduct = ids => {
  return http.delete(PORT1 + `/product/batch`, { ids });
};

// 更新商品状态 (预留接口)
export const updateProductStatus = params => {
  return http.put(PORT1 + `/product/status`, params);
};

// 导出商品数据/模板
export const exportProductInfo = (params = {}) => {
  console.log('导出商品数据参数:', params);
  // 根据API文档，使用download方法处理文件下载
  // API路径: /jnl_/products/export
  return http.download(PORT1 + `/products/export`, params, {
    loading: true,
    responseType: 'blob'
  });
};

// 批量导入商品
export const batchAddProduct = (formData) => {
  console.log('批量导入商品:', formData);
  // API路径: /jnl_/products/import
  return http.upload(PORT1 + `/products/import`, formData, {
    loading: true
  });
};

// 获取商品状态枚举
export const getProductStatus = () => {
  return [
    { label: "在售", value: 1 },
    { label: "下架", value: 0 },
    { label: "缺货", value: 2 }
  ];
};

// 获取商品分类枚举 (从产品分类接口获取)
export const getProductCategories = () => {
  return http.get(PORT1 + `/f_product/all`);
};
