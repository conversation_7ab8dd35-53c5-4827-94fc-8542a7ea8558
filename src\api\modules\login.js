import { PORT1 } from "@/api/config/servicePort";
import authMenuList from "@/assets/json/authMenuList.json";
import permissionLevel from "@/assets/json/permissionLevel.json";
import http from "@/api";

/**
 * @name JNL-Shop 认证授权模块
 * @description 符合企业级开发规范的认证授权接口
 */

// ==================== 认证管理接口 ====================

/**
 * @description 用户登录 - 符合后端LoginRequestDTO
 * @param {Object} params 登录参数
 * @param {string} params.username 用户名（手机号或邮箱）
 * @param {string} params.password 密码
 * @param {boolean} [params.rememberMe] 记住我，默认false
 * @returns {Promise} 登录响应
 */
export const loginApi = (params) => {
  return http.post(PORT1 + "/auth/login", params);
};

/**
 * @description 用户注册
 * @param {Object} params 注册参数
 * @param {string} params.username 用户名
 * @param {string} params.password 密码
 * @param {string} params.confirmPassword 确认密码
 * @param {string} params.mobile 手机号
 * @param {string} [params.email] 邮箱
 * @param {string} [params.realName] 真实姓名
 * @param {string} params.smsCode 短信验证码
 * @param {string} [params.emailCode] 邮箱验证码
 * @returns {Promise} 注册响应
 */
export const registerApi = (params) => {
  return http.post(PORT1 + "/auth/register", params, { loading: true });
};

/**
 * @description 发送验证码
 * @param {Object} params 验证码参数
 * @param {string} params.type 验证码类型(sms/email)
 * @param {string} [params.mobile] 手机号(type=sms时必填)
 * @param {string} [params.email] 邮箱(type=email时必填)
 * @param {string} params.scene 业务场景(register/login/reset_password)
 * @returns {Promise} 发送响应
 */
export const sendCodeApi = (params) => {
  return http.post(PORT1 + "/auth/send-code", params, { loading: true });
};

/**
 * @description 用户登出
 * @returns {Promise} 登出响应
 */
export const logoutApi = () => {
  return http.post(PORT1 + "/auth/logout", {}, { loading: true });
};

/**
 * @description 刷新Token
 * @returns {Promise} 刷新响应
 */
export const refreshTokenApi = () => {
  return http.post(PORT1 + "/auth/refresh-token", {}, { loading: false });
};

/**
 * @description 验证Token
 * @returns {Promise} 验证响应
 */
export const validateTokenApi = () => {
  return http.get(PORT1 + "/auth/validate-token", {}, { loading: false });
};

/**
 * @description 获取当前用户信息
 * @returns {Promise} 用户信息响应
 */
export const getCurrentUserApi = () => {
  return http.get(PORT1 + "/auth/current-user", {}, { loading: false });
};

/**
 * @description 修改密码
 * @param {Object} params 修改密码参数
 * @param {string} params.oldPassword 旧密码
 * @param {string} params.newPassword 新密码
 * @returns {Promise} 修改响应
 */
export const changePasswordApi = (params) => {
  return http.postForm(PORT1 + "/auth/change-password", params, { loading: true });
};

/**
 * @description 重置密码
 * @param {Object} params 重置密码参数
 * @param {string} params.mobile 手机号
 * @param {string} params.smsCode 短信验证码
 * @param {string} params.newPassword 新密码
 * @returns {Promise} 重置响应
 */
export const resetPasswordApi = (params) => {
  return http.postForm(PORT1 + "/auth/reset-password", params, { loading: true });
};

// ==================== 权限管理接口 ====================

/**
 * @description 授权用户权限 (仅root用户可用)
 * @param {Object} params 授权参数
 * @param {number} params.targetUserId 目标用户ID
 * @param {string} params.permissionLevel 权限等级(root/admin/operator/guest)
 * @returns {Promise} 授权响应
 */
export const grantPermissionApi = (params) => {
  return http.postForm(PORT1 + "/permission/grant", params, { loading: true });
};

// ==================== 兼容性接口 (逐步迁移) ====================

/**
 * @description 获取菜单列表 (兼容旧版本)
 * @returns {Promise} 菜单列表
 */
export const getAuthMenuListApi = () => {
  // 生产环境使用真实接口
  // return http.get(PORT1 + "/menu/list", {}, { loading: false });

  // 开发环境使用本地数据
  return authMenuList;
};

/**
 * @description 获取用户权限等级 (兼容旧版本，建议使用getCurrentUserApi)
 * @returns {Promise} 权限等级
 */
export const getAuthButtonListApi = () => {
  // 生产环境使用真实接口
  // return getCurrentUserApi().then(response => ({
  //   code: response.code,
  //   data: { permission: response.data.permissionLevel },
  //   message: response.message
  // }));

  // 开发环境使用本地数据
  return permissionLevel;
};
