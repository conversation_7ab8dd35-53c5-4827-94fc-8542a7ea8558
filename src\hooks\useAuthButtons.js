import { computed } from "vue";
import { useRoute } from "vue-router";
import { useAuthStore } from "@/stores/modules/auth";
import { getPermissionsByLevel } from "@/config/permissions";

/**
 * @description 页面按钮权限 (基于权限等级)
 * */
export const useAuthButtons = () => {
  const route = useRoute();
  const authStore = useAuthStore();

  // 基于用户权限等级获取按钮权限
  const authButtons = computed(() => {
    const permissionLevel = authStore.permissionLevelGet;
    return getPermissionsByLevel(permissionLevel);
  });

  const BUTTONS = computed(() => {
    let currentPageAuthButton = {};
    authButtons.value.forEach(item => (currentPageAuthButton[item] = true));
    return currentPageAuthButton;
  });

  return {
    BUTTONS,
    // 额外返回权限等级和权限列表，方便调试和显示
    permissionLevel: computed(() => authStore.permissionLevelGet),
    permissions: authButtons
  };
};
