<template>
  <el-dialog
    v-model="dialogVisible"
    title="导出日志"
    width="500px"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <el-form :model="exportForm" :rules="rules" ref="formRef" label-width="120px">
      <el-form-item label="导出格式" prop="format">
        <el-radio-group v-model="exportForm.format">
          <el-radio label="excel">Excel</el-radio>
          <el-radio label="csv">CSV</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="时间范围" prop="timeRange">
        <el-date-picker
          v-model="exportForm.timeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="功能模块" prop="module">
        <el-select
          v-model="exportForm.module"
          placeholder="请选择功能模块"
          clearable
          filterable
          style="width: 100%"
        >
          <el-option
            v-for="item in moduleOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="操作状态" prop="status">
        <el-select
          v-model="exportForm.status"
          placeholder="请选择操作状态"
          clearable
          style="width: 100%"
        >
          <el-option label="成功" :value="1" />
          <el-option label="失败" :value="0" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="预计导出">
        <div class="export-preview">
          <el-button type="primary" plain @click="previewExport" :loading="previewLoading">
            预览导出结果
          </el-button>
          <div v-if="previewResult.total > 0" class="preview-result">
            <el-tag type="info" size="large">
              预计导出 {{ previewResult.total }} 条日志记录
            </el-tag>
          </div>
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="executeExport" 
          :loading="exportLoading"
          :disabled="previewResult.total === 0"
        >
          开始导出
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { exportSystemLog, getSystemLogPage, getModuleSystemLog } from "@/api/modules/systemLog";

// 对话框可见性
const dialogVisible = ref(false);

// 表单引用
const formRef = ref(null);

// 加载状态
const previewLoading = ref(false);
const exportLoading = ref(false);

// 模块选项
const moduleOptions = ref([]);

// 导出表单
const exportForm = reactive({
  format: 'excel',
  timeRange: null,
  module: '',
  status: null
});

// 预览结果
const previewResult = reactive({
  total: 0
});

// 表单验证规则
const rules = {
  format: [
    { required: true, message: '请选择导出格式', trigger: 'change' }
  ]
};

/**
 * 获取模块列表
 */
const fetchModuleOptions = async () => {
  try {
    // 这里应该调用获取模块列表的接口
    // 由于API文档中没有提供获取模块列表的接口，这里模拟一些常用模块
    moduleOptions.value = [
      { label: '用户管理', value: '用户管理' },
      { label: '角色管理', value: '角色管理' },
      { label: '菜单管理', value: '菜单管理' },
      { label: '部门管理', value: '部门管理' },
      { label: '字典管理', value: '字典管理' },
      { label: '文件管理', value: '文件管理' },
      { label: '系统日志', value: '系统日志' },
      { label: '商品管理', value: '商品管理' },
      { label: '订单管理', value: '订单管理' }
    ];
  } catch (error) {
    ElMessage.error('获取模块列表失败');
  }
};

/**
 * 预览导出结果
 */
const previewExport = async () => {
  try {
    previewLoading.value = true;
    
    // 构建查询参数
    const params = {};
    
    if (exportForm.timeRange && exportForm.timeRange.length === 2) {
      params.startTime = exportForm.timeRange[0];
      params.endTime = exportForm.timeRange[1];
    }
    
    if (exportForm.module) {
      params.module = exportForm.module;
    }
    
    if (exportForm.status !== null) {
      params.status = exportForm.status;
    }
    
    // 查询要导出的日志数量
    const { data } = await getSystemLogPage({
      current: 1,
      size: 1,
      ...params
    });
    
    previewResult.total = data.total || 0;
    
    if (previewResult.total > 0) {
      ElMessage.success(`预览完成，将导出 ${previewResult.total} 条日志记录`);
    } else {
      ElMessage.info('没有符合条件的日志记录');
    }
  } catch (error) {
    ElMessage.error('预览失败，请重试');
  } finally {
    previewLoading.value = false;
  }
};

/**
 * 执行导出操作
 */
const executeExport = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) return;
    
    exportLoading.value = true;
    
    // 构建导出参数
    const params = {
      format: exportForm.format
    };
    
    if (exportForm.timeRange && exportForm.timeRange.length === 2) {
      params.startTime = exportForm.timeRange[0];
      params.endTime = exportForm.timeRange[1];
    }
    
    if (exportForm.module) {
      params.module = exportForm.module;
    }
    
    if (exportForm.status !== null) {
      params.status = exportForm.status;
    }
    
    // 执行导出
    const response = await exportSystemLog(params);
    
    // 处理文件下载
    const blob = new Blob([response.data], { 
      type: exportForm.format === 'excel' 
        ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
        : 'text/csv' 
    });
    
    const fileName = `系统日志_${new Date().toISOString().split('T')[0]}.${exportForm.format === 'excel' ? 'xlsx' : 'csv'}`;
    
    // 创建下载链接
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = fileName;
    link.click();
    
    // 释放URL对象
    URL.revokeObjectURL(link.href);
    
    ElMessage.success('导出成功');
    
    // 关闭对话框
    dialogVisible.value = false;
    
  } catch (error) {
    ElMessage.error('导出失败，请重试');
  } finally {
    exportLoading.value = false;
  }
};

/**
 * 重置表单
 */
const resetForm = () => {
  exportForm.format = 'excel';
  exportForm.timeRange = null;
  exportForm.module = '';
  exportForm.status = null;
  previewResult.total = 0;
  formRef.value?.clearValidate();
};

/**
 * 打开对话框
 */
const open = () => {
  resetForm();
  dialogVisible.value = true;
};

// 初始化
onMounted(() => {
  fetchModuleOptions();
});

// 暴露方法给父组件
defineExpose({
  open
});
</script>

<style scoped>
.export-preview {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.preview-result {
  display: flex;
  align-items: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
