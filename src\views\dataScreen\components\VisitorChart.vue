<template>
  <div class="visitor-chart">
    <div ref="visitorChartRef" class="chart-container"></div>
  </div>
</template>

<script setup name="VisitorChart">
import { ref, onMounted, onBeforeUnmount } from "vue";
import * as echarts from "echarts";
import { getVisitorDataApi } from "@/api/modules/dataScreen";

const visitorChartRef = ref();
let myChart = null;

// 模拟近15日访问量数据
const mockData = {
  dates: [],
  visitors: [],
  totalVisitors: 0
};

// 生成近15日的模拟数据
const generateMockData = () => {
  const today = new Date();
  let total = 0;
  
  for (let i = 14; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    mockData.dates.push(`${date.getMonth() + 1}/${date.getDate()}`);
    
    // 生成随机访问量数据 (500-3000)
    const visitors = Math.floor(Math.random() * 2500) + 500;
    mockData.visitors.push(visitors);
    total += visitors;
  }
  
  mockData.totalVisitors = total;
};

const initChart = () => {
  myChart = echarts.init(visitorChartRef.value);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        return `${params[0].name}<br/>访问量: ${params[0].value.toLocaleString()}`;
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#667eea',
      borderWidth: 1,
      textStyle: {
        color: '#2c3e50'
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: mockData.dates,
      axisLabel: {
        color: '#7f8c8d',
        fontSize: 11,
        rotate: 45
      },
      axisLine: {
        lineStyle: {
          color: '#ecf0f1'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#7f8c8d',
        fontSize: 11
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#ecf0f1',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '访问量',
        type: 'bar',
        data: mockData.visitors,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#667eea' },
            { offset: 1, color: '#764ba2' }
          ]),
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#f093fb' },
              { offset: 1, color: '#f5576c' }
            ]),
            shadowBlur: 10,
            shadowColor: 'rgba(102, 126, 234, 0.5)'
          }
        },
        barWidth: '60%'
      }
    ]
  };
  
  myChart.setOption(option);
};

const getVisitorData = async () => {
  try {
    // 保留API调用接口，但暂时使用模拟数据
    // const { data } = await getVisitorDataApi();
    generateMockData();
    console.log("获取访问量数据:", mockData);
  } catch (error) {
    console.error("获取访问量数据失败:", error);
  }
};

onMounted(() => {
  getVisitorData();
  initChart();
  
  window.addEventListener("resize", () => {
    if (myChart) {
      myChart.resize();
    }
  });
});

onBeforeUnmount(() => {
  if (myChart) {
    myChart.dispose();
  }
});
</script>

<style scoped lang="scss">
.visitor-chart {
  width: 100%;
  height: 100%;

  .chart-container {
    width: 100%;
    height: 100%;
    min-height: 250px;
  }
}
</style>
