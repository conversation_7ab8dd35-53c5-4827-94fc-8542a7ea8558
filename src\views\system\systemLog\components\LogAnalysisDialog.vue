<template>
  <el-dialog
    v-model="dialogVisible"
    title="日志分析"
    width="1000px"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <div class="analysis-container">
      <!-- 分析选项 -->
      <div class="analysis-options">
        <el-form :model="analysisForm" label-width="100px" inline>
          <el-form-item label="分析类型">
            <el-select v-model="analysisForm.type" @change="handleTypeChange">
              <el-option label="趋势分析" value="trend" />
              <el-option label="热门排行" value="ranking" />
              <el-option label="异常检测" value="anomaly" />
              <el-option label="性能分析" value="performance" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-select v-model="analysisForm.timeRange" @change="fetchAnalysisData">
              <el-option label="最近24小时" value="24h" />
              <el-option label="最近7天" value="7d" />
              <el-option label="最近30天" value="30d" />
              <el-option label="最近90天" value="90d" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="fetchAnalysisData" :loading="loading">
              开始分析
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 分析结果 -->
      <div class="analysis-results" v-loading="loading">
        <!-- 趋势分析 -->
        <div v-if="analysisForm.type === 'trend'" class="trend-analysis">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>操作趋势分析</span>
                <el-tag>{{ analysisForm.timeRange }}</el-tag>
              </div>
            </template>
            <div ref="trendChartRef" class="chart"></div>
          </el-card>
        </div>

        <!-- 热门排行 -->
        <div v-if="analysisForm.type === 'ranking'" class="ranking-analysis">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card shadow="hover">
                <template #header>热门操作排行</template>
                <div class="ranking-list">
                  <div 
                    v-for="(item, index) in rankingData.operations" 
                    :key="index"
                    class="ranking-item"
                  >
                    <div class="rank">{{ index + 1 }}</div>
                    <div class="name">{{ item.name }}</div>
                    <div class="count">{{ item.count }}</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card shadow="hover">
                <template #header>热门模块排行</template>
                <div class="ranking-list">
                  <div 
                    v-for="(item, index) in rankingData.modules" 
                    :key="index"
                    class="ranking-item"
                  >
                    <div class="rank">{{ index + 1 }}</div>
                    <div class="name">{{ item.name }}</div>
                    <div class="count">{{ item.count }}</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 异常检测 -->
        <div v-if="analysisForm.type === 'anomaly'" class="anomaly-analysis">
          <el-card shadow="hover">
            <template #header>异常检测结果</template>
            <el-table :data="anomalyData" stripe>
              <el-table-column prop="time" label="时间" width="180" />
              <el-table-column prop="type" label="异常类型" width="120">
                <template #default="scope">
                  <el-tag :type="getAnomalyTagType(scope.row.type)">
                    {{ scope.row.type }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="异常描述" />
              <el-table-column prop="severity" label="严重程度" width="100">
                <template #default="scope">
                  <el-tag :type="getSeverityTagType(scope.row.severity)">
                    {{ scope.row.severity }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="count" label="次数" width="80" />
            </el-table>
          </el-card>
        </div>

        <!-- 性能分析 -->
        <div v-if="analysisForm.type === 'performance'" class="performance-analysis">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-card shadow="hover">
                <template #header>响应时间分布</template>
                <div ref="responseTimeChartRef" class="chart-small"></div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card shadow="hover">
                <template #header>错误率统计</template>
                <div ref="errorRateChartRef" class="chart-small"></div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card shadow="hover">
                <template #header>并发量分析</template>
                <div ref="concurrencyChartRef" class="chart-small"></div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="exportAnalysisReport">导出报告</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, nextTick } from "vue";
import { ElMessage } from "element-plus";
import * as echarts from "echarts";
import { getSystemLogTrend, getSystemLogTopRanking } from "@/api/modules/systemLog";

// 对话框可见性
const dialogVisible = ref(false);

// 加载状态
const loading = ref(false);

// 图表引用
const trendChartRef = ref(null);
const responseTimeChartRef = ref(null);
const errorRateChartRef = ref(null);
const concurrencyChartRef = ref(null);

// 图表实例
let trendChart = null;
let responseTimeChart = null;
let errorRateChart = null;
let concurrencyChart = null;

// 分析表单
const analysisForm = reactive({
  type: 'trend',
  timeRange: '7d'
});

// 排行数据
const rankingData = reactive({
  operations: [],
  modules: []
});

// 异常数据
const anomalyData = ref([]);

/**
 * 获取分析数据
 */
const fetchAnalysisData = async () => {
  loading.value = true;
  try {
    switch (analysisForm.type) {
      case 'trend':
        await fetchTrendData();
        break;
      case 'ranking':
        await fetchRankingData();
        break;
      case 'anomaly':
        await fetchAnomalyData();
        break;
      case 'performance':
        await fetchPerformanceData();
        break;
    }
  } catch (error) {
    ElMessage.error('获取分析数据失败');
  } finally {
    loading.value = false;
  }
};

/**
 * 获取趋势数据
 */
const fetchTrendData = async () => {
  const days = parseInt(analysisForm.timeRange.replace(/\D/g, ''));
  const { data } = await getSystemLogTrend({
    timeRange: analysisForm.timeRange,
    groupBy: days <= 1 ? 'hour' : 'day'
  });
  
  nextTick(() => {
    initTrendChart(data);
  });
};

/**
 * 获取排行数据
 */
const fetchRankingData = async () => {
  const days = parseInt(analysisForm.timeRange.replace(/\D/g, ''));
  
  const [operationRanking, moduleRanking] = await Promise.all([
    getSystemLogTopRanking({ days, type: 'operation', limit: 10 }),
    getSystemLogTopRanking({ days, type: 'module', limit: 10 })
  ]);
  
  rankingData.operations = operationRanking.data || [];
  rankingData.modules = moduleRanking.data || [];
};

/**
 * 获取异常数据
 */
const fetchAnomalyData = async () => {
  // 模拟异常检测数据
  anomalyData.value = [
    {
      time: '2025-07-21 14:30:00',
      type: '高频访问',
      description: '用户在短时间内进行了大量操作',
      severity: '中等',
      count: 156
    },
    {
      time: '2025-07-21 13:15:00',
      type: '异常IP',
      description: '来自异常地区的访问',
      severity: '高',
      count: 23
    },
    {
      time: '2025-07-21 12:45:00',
      type: '错误率异常',
      description: '某模块错误率突然升高',
      severity: '中等',
      count: 45
    }
  ];
};

/**
 * 获取性能数据
 */
const fetchPerformanceData = async () => {
  nextTick(() => {
    initPerformanceCharts();
  });
};

/**
 * 初始化趋势图表
 */
const initTrendChart = (data) => {
  if (!trendChartRef.value) return;
  
  if (trendChart) {
    trendChart.dispose();
  }
  
  trendChart = echarts.init(trendChartRef.value);
  
  const option = {
    title: {
      text: '操作趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: data.times || []
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '操作次数',
        type: 'line',
        data: data.counts || [],
        smooth: true,
        itemStyle: {
          color: '#409EFF'
        }
      }
    ]
  };
  
  trendChart.setOption(option);
};

/**
 * 初始化性能图表
 */
const initPerformanceCharts = () => {
  // 响应时间分布图表
  if (responseTimeChartRef.value) {
    if (responseTimeChart) responseTimeChart.dispose();
    responseTimeChart = echarts.init(responseTimeChartRef.value);
    
    const responseTimeOption = {
      title: { text: '响应时间', textStyle: { fontSize: 14 } },
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: '60%',
        data: [
          { value: 60, name: '<100ms' },
          { value: 25, name: '100-500ms' },
          { value: 10, name: '500ms-1s' },
          { value: 5, name: '>1s' }
        ]
      }]
    };
    responseTimeChart.setOption(responseTimeOption);
  }
  
  // 错误率统计图表
  if (errorRateChartRef.value) {
    if (errorRateChart) errorRateChart.dispose();
    errorRateChart = echarts.init(errorRateChartRef.value);
    
    const errorRateOption = {
      title: { text: '错误率', textStyle: { fontSize: 14 } },
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: '60%',
        data: [
          { value: 95, name: '成功' },
          { value: 5, name: '失败' }
        ]
      }]
    };
    errorRateChart.setOption(errorRateOption);
  }
  
  // 并发量分析图表
  if (concurrencyChartRef.value) {
    if (concurrencyChart) concurrencyChart.dispose();
    concurrencyChart = echarts.init(concurrencyChartRef.value);
    
    const concurrencyOption = {
      title: { text: '并发量', textStyle: { fontSize: 14 } },
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'] },
      yAxis: { type: 'value' },
      series: [{
        type: 'line',
        data: [20, 15, 45, 80, 65, 35],
        smooth: true
      }]
    };
    concurrencyChart.setOption(concurrencyOption);
  }
};

/**
 * 处理类型变化
 */
const handleTypeChange = () => {
  fetchAnalysisData();
};

/**
 * 获取异常类型标签类型
 */
const getAnomalyTagType = (type) => {
  const typeMap = {
    '高频访问': 'warning',
    '异常IP': 'danger',
    '错误率异常': 'warning'
  };
  return typeMap[type] || 'info';
};

/**
 * 获取严重程度标签类型
 */
const getSeverityTagType = (severity) => {
  const severityMap = {
    '高': 'danger',
    '中等': 'warning',
    '低': 'info'
  };
  return severityMap[severity] || 'info';
};

/**
 * 导出分析报告
 */
const exportAnalysisReport = () => {
  ElMessage.success('分析报告导出功能开发中...');
};

/**
 * 打开对话框
 */
const open = () => {
  dialogVisible.value = true;
  fetchAnalysisData();
};

// 监听窗口大小变化
window.addEventListener('resize', () => {
  trendChart?.resize();
  responseTimeChart?.resize();
  errorRateChart?.resize();
  concurrencyChart?.resize();
});

// 暴露方法给父组件
defineExpose({
  open
});
</script>

<style scoped>
.analysis-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.analysis-options {
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.analysis-results {
  min-height: 400px;
}

.chart {
  width: 100%;
  height: 400px;
}

.chart-small {
  width: 100%;
  height: 250px;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.rank {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #409EFF;
  color: white;
  border-radius: 50%;
  font-weight: bold;
  margin-right: 15px;
}

.name {
  flex: 1;
  font-weight: 500;
}

.count {
  color: #409EFF;
  font-weight: bold;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
