/**
 * @description 表单验证工具函数
 * <AUTHOR> Team
 */

/**
 * @description 验证用户名
 * @param {string} username 用户名
 * @returns {boolean} 验证结果
 */
export const validateUsername = (username) => {
  const reg = /^[a-zA-Z0-9_]{4,20}$/;
  return reg.test(username);
};

/**
 * @description 验证密码强度
 * @param {string} password 密码
 * @returns {boolean} 验证结果
 */
export const validatePassword = (password) => {
  // 8-32位，包含字母和数字
  const reg = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,32}$/;
  return reg.test(password);
};

/**
 * @description 验证手机号
 * @param {string} mobile 手机号
 * @returns {boolean} 验证结果
 */
export const validateMobile = (mobile) => {
  const reg = /^1[3-9]\d{9}$/;
  return reg.test(mobile);
};

/**
 * @description 验证邮箱
 * @param {string} email 邮箱
 * @returns {boolean} 验证结果
 */
export const validateEmail = (email) => {
  const reg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return reg.test(email);
};

/**
 * @description 验证验证码
 * @param {string} code 验证码
 * @returns {boolean} 验证结果
 */
export const validateVerifyCode = (code) => {
  const reg = /^\d{6}$/;
  return reg.test(code);
};

/**
 * @description 验证身份证号
 * @param {string} idCard 身份证号
 * @returns {boolean} 验证结果
 */
export const validateIdCard = (idCard) => {
  const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  return reg.test(idCard);
};

/**
 * @description 验证URL
 * @param {string} url URL地址
 * @returns {boolean} 验证结果
 */
export const validateURL = (url) => {
  const reg = /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/i;
  return reg.test(url);
};

/**
 * @description 验证IP地址
 * @param {string} ip IP地址
 * @returns {boolean} 验证结果
 */
export const validateIP = (ip) => {
  const reg = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  return reg.test(ip);
};

/**
 * @description 验证端口号
 * @param {string|number} port 端口号
 * @returns {boolean} 验证结果
 */
export const validatePort = (port) => {
  const portNum = Number(port);
  return Number.isInteger(portNum) && portNum >= 1 && portNum <= 65535;
};

/**
 * @description 验证中文姓名
 * @param {string} name 姓名
 * @returns {boolean} 验证结果
 */
export const validateChineseName = (name) => {
  const reg = /^[\u4e00-\u9fa5]{2,10}$/;
  return reg.test(name);
};

/**
 * @description 验证银行卡号
 * @param {string} cardNumber 银行卡号
 * @returns {boolean} 验证结果
 */
export const validateBankCard = (cardNumber) => {
  const reg = /^[1-9]\d{12,19}$/;
  return reg.test(cardNumber);
};

/**
 * @description 验证统一社会信用代码
 * @param {string} code 统一社会信用代码
 * @returns {boolean} 验证结果
 */
export const validateSocialCreditCode = (code) => {
  const reg = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/;
  return reg.test(code);
};

/**
 * @description 验证车牌号
 * @param {string} plateNumber 车牌号
 * @returns {boolean} 验证结果
 */
export const validatePlateNumber = (plateNumber) => {
  const reg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/;
  return reg.test(plateNumber);
};

/**
 * @description 验证QQ号
 * @param {string} qq QQ号
 * @returns {boolean} 验证结果
 */
export const validateQQ = (qq) => {
  const reg = /^[1-9][0-9]{4,10}$/;
  return reg.test(qq);
};

/**
 * @description 验证微信号
 * @param {string} wechat 微信号
 * @returns {boolean} 验证结果
 */
export const validateWechat = (wechat) => {
  const reg = /^[a-zA-Z][-_a-zA-Z0-9]{5,19}$/;
  return reg.test(wechat);
};

/**
 * @description 验证邮政编码
 * @param {string} zipCode 邮政编码
 * @returns {boolean} 验证结果
 */
export const validateZipCode = (zipCode) => {
  const reg = /^[1-9]\d{5}$/;
  return reg.test(zipCode);
};

/**
 * @description 验证经度
 * @param {string|number} longitude 经度
 * @returns {boolean} 验证结果
 */
export const validateLongitude = (longitude) => {
  const reg = /^[-]?(0?\d{1,2}\.\d{1,6}|1[0-7]?\d{1}\.\d{1,6}|180\.0{1,6})$/;
  return reg.test(longitude);
};

/**
 * @description 验证纬度
 * @param {string|number} latitude 纬度
 * @returns {boolean} 验证结果
 */
export const validateLatitude = (latitude) => {
  const reg = /^[-]?([0-8]?\d{1}\.\d{1,6}|90\.0{1,6})$/;
  return reg.test(latitude);
};

/**
 * @description 验证MAC地址
 * @param {string} mac MAC地址
 * @returns {boolean} 验证结果
 */
export const validateMAC = (mac) => {
  const reg = /^[A-Fa-f0-9]{2}:[A-Fa-f0-9]{2}:[A-Fa-f0-9]{2}:[A-Fa-f0-9]{2}:[A-Fa-f0-9]{2}:[A-Fa-f0-9]{2}$/;
  return reg.test(mac);
};

/**
 * @description 验证IPv6地址
 * @param {string} ipv6 IPv6地址
 * @returns {boolean} 验证结果
 */
export const validateIPv6 = (ipv6) => {
  const reg = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
  return reg.test(ipv6);
};

/**
 * @description 验证文件扩展名
 * @param {string} filename 文件名
 * @param {string[]} allowedExtensions 允许的扩展名数组
 * @returns {boolean} 验证结果
 */
export const validateFileExtension = (filename, allowedExtensions) => {
  if (!filename || !allowedExtensions || !Array.isArray(allowedExtensions)) {
    return false;
  }
  
  const extension = filename.split('.').pop()?.toLowerCase();
  return allowedExtensions.includes(extension);
};

/**
 * @description 验证文件大小
 * @param {number} fileSize 文件大小(字节)
 * @param {number} maxSize 最大大小(字节)
 * @returns {boolean} 验证结果
 */
export const validateFileSize = (fileSize, maxSize) => {
  return fileSize <= maxSize;
};

/**
 * @description 验证JSON字符串
 * @param {string} jsonString JSON字符串
 * @returns {boolean} 验证结果
 */
export const validateJSON = (jsonString) => {
  try {
    JSON.parse(jsonString);
    return true;
  } catch (e) {
    return false;
  }
};

/**
 * @description 验证Base64字符串
 * @param {string} base64String Base64字符串
 * @returns {boolean} 验证结果
 */
export const validateBase64 = (base64String) => {
  const reg = /^[A-Za-z0-9+/]*={0,2}$/;
  return reg.test(base64String) && base64String.length % 4 === 0;
};

/**
 * @description 验证颜色值(HEX)
 * @param {string} color 颜色值
 * @returns {boolean} 验证结果
 */
export const validateHexColor = (color) => {
  const reg = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  return reg.test(color);
};

/**
 * @description 验证RGB颜色值
 * @param {string} color RGB颜色值
 * @returns {boolean} 验证结果
 */
export const validateRGBColor = (color) => {
  const reg = /^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/;
  return reg.test(color);
};

/**
 * @description 验证版本号
 * @param {string} version 版本号
 * @returns {boolean} 验证结果
 */
export const validateVersion = (version) => {
  const reg = /^\d+\.\d+\.\d+$/;
  return reg.test(version);
};
