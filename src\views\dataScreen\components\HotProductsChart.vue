<template>
  <div class="hot-products-chart">
    <div class="product-list">
      <div 
        v-for="(product, index) in hotProducts" 
        :key="product.id"
        class="product-item"
        :class="{ 'top-three': index < 3 }"
      >
        <div class="rank" :class="{
          'top-1': index === 0,
          'top-2': index === 1,
          'top-3': index === 2
        }">{{ index + 1 }}</div>
        <div class="product-info">
          <div class="product-name">{{ product.name }}</div>
          <div class="product-sales">销量: {{ product.sales }}</div>
        </div>
        <div class="product-revenue">¥{{ product.revenue.toLocaleString() }}</div>
      </div>
    </div>
  </div>
</template>

<script setup name="HotProductsChart">
import { ref, onMounted } from "vue";
import { getHotProductsDataApi } from "@/api/modules/dataScreen";

const hotProducts = ref([]);

// 模拟热销产品数据
const mockData = [
  { id: 1, name: "高强度从动轮", sales: 1548, revenue: 185760 },
  { id: 2, name: "精密主动轮", sales: 1234, revenue: 148080 },
  { id: 3, name: "不锈钢螺丝套装", sales: 987, revenue: 98700 },
  { id: 4, name: "耐磨胶轮", sales: 856, revenue: 85600 },
  { id: 5, name: "重型铁轮", sales: 543, revenue: 65160 },
  { id: 6, name: "万向轮组合", sales: 432, revenue: 51840 },
  { id: 7, name: "工业滚轮", sales: 321, revenue: 38520 },
  { id: 8, name: "减震轮", sales: 298, revenue: 35760 }
];

const getHotProductsData = async () => {
  try {
    // 保留API调用接口，但暂时使用模拟数据
    // const { data } = await getHotProductsDataApi();
    hotProducts.value = mockData;
    console.log("获取热销产品数据:", mockData);
  } catch (error) {
    console.error("获取热销产品数据失败:", error);
  }
};

onMounted(() => {
  getHotProductsData();
});
</script>

<style scoped lang="scss">
.hot-products-chart {
  width: 100%;
  height: 100%;
  padding: 8px;
  box-sizing: border-box;
  
  .product-list {
    height: calc(100% - 16px);
    overflow-y: auto;
    padding: 0;
    box-sizing: border-box;
    
    .product-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      margin-bottom: 8px;
      background: rgba(0, 0, 0, 0.02);
      border-radius: 12px;
      border-left: 4px solid transparent;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(0, 0, 0, 0.05);
        transform: translateX(4px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.top-three {
        border-left-color: #667eea;
        background: linear-gradient(90deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
      }
      
      .rank {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #ecf0f1;
        color: #2c3e50;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: bold;
        margin-right: 16px;
        flex-shrink: 0;

        &.top-1 {
          background: linear-gradient(45deg, #667eea, #764ba2);
          color: white;
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        &.top-2 {
          background: linear-gradient(45deg, #f093fb, #f5576c);
          color: white;
          box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);
        }

        &.top-3 {
          background: linear-gradient(45deg, #4facfe, #00f2fe);
          color: white;
          box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
        }
      }
      
      .product-info {
        flex: 1;
        min-width: 0;
        
        .product-name {
          color: #2c3e50;
          font-size: 14px;
          font-weight: 600;
          margin-bottom: 6px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .product-sales {
          color: #667eea;
          font-size: 12px;
          font-weight: 500;
        }
      }

      .product-revenue {
        color: #27ae60;
        font-size: 12px;
        font-weight: 600;
        flex-shrink: 0;
      }
    }
  }
}

/* 自定义滚动条 */
.product-list::-webkit-scrollbar {
  width: 6px;
}

.product-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.product-list::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 3px;
}

.product-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #764ba2, #667eea);
}
</style>
