import router from "@/routers/index";
import { LOGIN_URL } from "@/config";
import { ElNotification } from "element-plus";
import { useUserStore } from "@/stores/modules/user";
import { useAuthStore } from "@/stores/modules/auth";

// 引入 views 文件夹下所有 vue 文件
const modules = import.meta.glob("@/views/**/*.vue");

/**
 * @description 初始化动态路由
 */
export const initDynamicRouter = async () => {
  const userStore = useUserStore();
  const authStore = useAuthStore();

  try {
    // console.log("开始获取菜单和权限数据...");

    // 1.获取菜单列表 && 按钮权限列表
    await authStore.getAuthMenuList();
    // console.log("菜单列表获取完成:", authStore.authMenuListGet);

    await authStore.getAuthButtonList();
    // console.log("权限等级获取完成:", authStore.permissionLevelGet);

    // 2.判断当前用户有没有菜单权限
    if (!authStore.authMenuListGet.length) {
      console.error("用户没有菜单权限");
      ElNotification({
        title: "无权限访问",
        message: "当前账号无任何菜单权限，请联系系统管理员！",
        type: "warning",
        duration: 3000
      });
      userStore.setToken("");
      router.replace(LOGIN_URL);
      return Promise.reject("No permission");
    }

    // console.log("开始添加动态路由...");
    // console.log("扁平化菜单列表:", authStore.flatMenuListGet);

    // 3.添加动态路由
    authStore.flatMenuListGet.forEach((item, index) => {
      // console.log(`添加路由 ${index + 1}:`, item);
      item.children && delete item.children;
      if (item.component && typeof item.component == "string") {
        item.component = modules["/src/views" + item.component + ".vue"];
      }
      if (item.meta.isFull) {
        router.addRoute(item);
        // console.log(`已添加全屏路由: ${item.path}`);
      } else {
        router.addRoute("layout", item);
        // console.log(`已添加布局路由: ${item.path}`);
      }
    });

    // console.log("动态路由添加完成");
    // console.log("当前所有路由:", router.getRoutes().map(r => r.path));

  } catch (error) {
    console.error("动态路由初始化失败:", error);
    // 当按钮 || 菜单请求出错时，重定向到登陆页
    userStore.setToken("");
    router.replace(LOGIN_URL);
    return Promise.reject(error);
  }
};
