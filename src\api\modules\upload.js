import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

/**
 * @name 文件上传模块
 */
// 图片上传
export const uploadImg = params => {
  return http.post(PORT1 + `/file/upload/img`, params, { cancel: false });
};

// 视频上传
export const uploadVideo = params => {
  return http.post(PORT1 + `/file/upload/video`, params, { cancel: false });
};

// MinIO健康检查
export const minioHealthCheck = () => {
  return http.get(PORT1 + `/minio/health`);
};

// MinIO批量上传文件夹中的图片
export const batchUploadFromFolder = params => {
  return http.post(PORT1 + `/minio/batch-upload`, params);
};
