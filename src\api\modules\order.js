import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

/**
 * @name 订单管理模块
 */

// 分页查询订单列表
export const getOrderPage = params => {
  const { current = 1, size = 50, ...queryDTO } = params;
  return http.post(PORT1 + `/order/page?current=${current}&size=${size}`, queryDTO);
};

// 查询订单详情
export const getOrderDetail = orderId => {
  return http.get(PORT1 + `/order/${orderId}`);
};

// 查询用户订单列表
export const getOrdersByUserId = (userId, status) => {
  const params = status ? { status } : {};
  return http.get(PORT1 + `/order/user/${userId}`, params);
};

// 更新订单信息
export const updateOrder = params => {
  return http.put(PORT1 + `/order`, params);
};

// 更新订单状态
export const updateOrderStatus = params => {
  return http.put(PORT1 + `/order/status`, params);
};

// 获取订单统计数据
export const getOrderStatistics = params => {
  return http.get(PORT1 + `/order/statistics`, params);
};

// 导出订单数据
export const exportOrders = params => {
  return http.download(PORT1 + `/order/export`, params);
};

// 获取订单状态列表
export const getOrderStatusList = () => {
  return http.get(PORT1 + `/order/status/list`);
};
