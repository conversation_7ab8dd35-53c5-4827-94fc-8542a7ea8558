import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

/**
 * @name 产品分类管理模块
 */

// 获取所有分类列表（树形结构）
export const getCategoryList = () => {
  return http.get(PORT1 + `/category/list`);
};

// 新增分类
export const addCategory = params => {
  return http.post(PORT1 + `/category/add`, params);
};

// 更新分类
export const updateCategory = (id, params) => {
  return http.put(PORT1 + `/category/update/${id}`, params);
};

// 删除分类
export const deleteCategory = id => {
  return http.delete(PORT1 + `/category/delete`, { id });
};
