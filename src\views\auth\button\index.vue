<template>
  <div class="card content-box">
    <span class="text"> 按钮权限 🍓🍇🍈🍉</span>
    <el-alert
      class="mb20"
      :title="`当前用户权限等级：${permissionLevel || '未设置'}`"
      type="info"
      :closable="false"
    />
    <el-alert
      class="mb20"
      :title="`当前用户按钮权限：${JSON.stringify(permissions)}`"
      type="success"
      :closable="false"
    />

    <el-divider content-position="left"> 权限等级切换演示 </el-divider>
    <el-row class="mb20">
      <el-button-group>
        <el-button
          :type="permissionLevel === 'root' ? 'primary' : 'default'"
          @click="switchPermission('root')"
        >
          Root权限
        </el-button>
        <el-button
          :type="permissionLevel === 'admin' ? 'primary' : 'default'"
          @click="switchPermission('admin')"
        >
          Admin权限
        </el-button>
        <el-button
          :type="permissionLevel === 'operator' ? 'primary' : 'default'"
          @click="switchPermission('operator')"
        >
          Operator权限
        </el-button>
      </el-button-group>
    </el-row>

    <el-divider content-position="left"> 权限等级说明 </el-divider>
    <el-row class="mb20">
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <span>🔑 Root权限</span>
          </template>
          <p>超级管理员，拥有所有权限</p>
          <el-tag type="success" size="small">add</el-tag>
          <el-tag type="success" size="small">edit</el-tag>
          <el-tag type="success" size="small">delete</el-tag>
          <el-tag type="success" size="small">import</el-tag>
          <el-tag type="success" size="small">export</el-tag>
          <el-tag type="success" size="small">upload</el-tag>
          <el-tag type="success" size="small">view</el-tag>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <span>👨‍💼 Admin权限</span>
          </template>
          <p>管理员，无删除和上传权限</p>
          <el-tag type="warning" size="small">add</el-tag>
          <el-tag type="warning" size="small">edit</el-tag>
          <el-tag type="info" size="small">delete</el-tag>
          <el-tag type="warning" size="small">import</el-tag>
          <el-tag type="warning" size="small">export</el-tag>
          <el-tag type="info" size="small">upload</el-tag>
          <el-tag type="warning" size="small">view</el-tag>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <span>👤 Operator权限</span>
          </template>
          <p>操作员，只能查看和导出</p>
          <el-tag type="info" size="small">add</el-tag>
          <el-tag type="info" size="small">edit</el-tag>
          <el-tag type="info" size="small">delete</el-tag>
          <el-tag type="info" size="small">import</el-tag>
          <el-tag type="danger" size="small">export</el-tag>
          <el-tag type="info" size="small">upload</el-tag>
          <el-tag type="danger" size="small">view</el-tag>
        </el-card>
      </el-col>
    </el-row>

    <el-divider content-position="left"> 使用 Hooks 方式绑定权限 </el-divider>
    <el-row class="mb20">
      <el-button v-if="BUTTONS.add" type="primary" :icon="CirclePlus"> 新增 </el-button>
      <el-button v-if="BUTTONS.edit" type="warning" :icon="EditPen"> 编辑 </el-button>
      <el-button v-if="BUTTONS.delete" type="danger" plain :icon="Delete"> 删除 </el-button>
      <el-button v-if="BUTTONS.import" type="info" plain :icon="Upload"> 导入数据 </el-button>
      <el-button v-if="BUTTONS.export" type="info" plain :icon="Download"> 导出数据 </el-button>
    </el-row>
    <el-divider content-position="left"> 使用 v-auth 指令绑定单个权限 </el-divider>
    <el-row class="mb20">
      <el-button v-auth="'add'" type="primary" :icon="CirclePlus"> 新增 </el-button>
      <el-button v-auth="'edit'" type="warning" :icon="EditPen"> 编辑 </el-button>
      <el-button v-auth="'delete'" type="danger" plain :icon="Delete"> 删除 </el-button>
      <el-button v-auth="'import'" type="info" plain :icon="Upload"> 导入数据 </el-button>
      <el-button v-auth="'export'" type="info" plain :icon="Download"> 导出数据 </el-button>
    </el-row>
    <el-divider content-position="left"> 使用 v-auth 指令绑定多个权限 </el-divider>
    <el-row>
      <el-button v-auth="['add', 'edit', 'delete', 'import', 'export']" type="primary" :icon="CirclePlus"> 新增 </el-button>
      <el-button v-auth="['add', 'edit', 'delete', 'import', 'export']" type="warning" :icon="EditPen"> 编辑 </el-button>
      <el-button v-auth="['add', 'edit', 'delete', 'import', 'export']" type="danger" plain :icon="Delete"> 删除 </el-button>
      <el-button v-auth="['add', 'edit', 'delete', 'import', 'export']" type="info" plain :icon="Upload"> 导入数据 </el-button>
      <el-button v-auth="['add', 'edit', 'delete', 'import', 'export']" type="info" plain :icon="Download"> 导出数据 </el-button>
    </el-row>
  </div>
</template>

<script setup name="authButton">
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { useAuthStore } from "@/stores/modules/auth";
import { CirclePlus, Delete, EditPen, Download, Upload } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

const { BUTTONS, permissionLevel, permissions } = useAuthButtons();
const authStore = useAuthStore();

// 切换权限等级的方法
const switchPermission = (level) => {
  authStore.setPermissionLevel(level);
  ElMessage.success(`权限等级已切换为：${level}`);
};
</script>

<style scoped lang="scss">
@import "./index.scss";

.el-col {
  padding: 0 8px;
}

.el-tag {
  margin: 2px 4px 2px 0;
}

.el-card {
  margin-bottom: 16px;
}
</style>
