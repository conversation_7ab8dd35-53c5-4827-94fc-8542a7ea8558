import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

/**
 * @name 系统日志管理模块
 * @description 符合企业级开发规范的系统日志管理接口
 */

// ==================== 系统日志查询接口 ====================

/**
 * @description 分页查询操作日志
 * @param {Object} params 查询参数
 * @param {number} [params.current=1] 页码
 * @param {number} [params.size=10] 每页大小
 * @param {number} [params.userId] 用户ID
 * @param {string} [params.username] 用户名（支持模糊查询）
 * @param {string} [params.module] 功能模块（支持模糊查询）
 * @param {string} [params.operation] 操作内容（支持模糊查询）
 * @param {string} [params.requestMethod] 请求方式（GET、POST等）
 * @param {string} [params.ip] 操作IP（支持模糊查询）
 * @param {number} [params.status] 操作状态（1成功 0失败）
 * @param {string} [params.startTime] 开始时间（格式：yyyy-MM-dd HH:mm:ss）
 * @param {string} [params.endTime] 结束时间（格式：yyyy-MM-dd HH:mm:ss）
 * @param {string} [params.keyword] 关键字搜索（匹配用户名、模块、操作内容）
 * @param {string} [params.orderBy=create_time] 排序字段
 * @param {string} [params.orderDirection=desc] 排序方式（asc或desc）
 * @returns {Promise} 分页查询响应
 */
export const getSystemLogPage = (params = {}) => {
  console.log('系统日志API请求参数:', params);
  return http.get(PORT1 + `/system/log/page`, params);
};

/**
 * @description 查询用户操作日志
 * @param {number} userId 用户ID
 * @param {Object} params 查询参数
 * @param {number} [params.limit=50] 限制返回的日志数量
 * @returns {Promise} 用户日志响应
 */
export const getUserSystemLog = (userId, params = {}) => {
  return http.get(PORT1 + `/system/log/user/${userId}`, params);
};

/**
 * @description 查询模块操作日志
 * @param {string} module 功能模块名称
 * @param {Object} params 查询参数
 * @param {number} [params.limit=50] 限制返回的日志数量
 * @returns {Promise} 模块日志响应
 */
export const getModuleSystemLog = (module, params = {}) => {
  return http.get(PORT1 + `/system/log/module/${encodeURIComponent(module)}`, params);
};

/**
 * @description 获取操作统计信息
 * @param {Object} params 查询参数
 * @param {number} [params.days=7] 统计天数
 * @returns {Promise} 统计信息响应
 */
export const getSystemLogStatistics = (params = {}) => {
  return http.get(PORT1 + `/system/log/statistics`, params);
};

/**
 * @description 查询日志详情
 * @param {number} id 日志ID
 * @returns {Promise} 日志详情响应
 */
export const getSystemLogDetail = (id) => {
  return http.get(PORT1 + `/system/log/${id}`);
};

// ==================== 系统日志管理接口 ====================

/**
 * @description 清理过期日志
 * @param {Object} params 清理参数
 * @param {number} [params.days=30] 保留天数（超过此天数的日志将被删除）
 * @returns {Promise} 清理响应
 */
export const cleanExpiredSystemLog = (params = {}) => {
  return http.delete(PORT1 + `/system/log/clean`, params);
};

// ==================== 系统日志导出接口 ====================

/**
 * @description 导出系统日志
 * @param {Object} params 导出参数
 * @param {string} [params.format='excel'] 导出格式（excel/csv）
 * @param {string} [params.startTime] 开始时间
 * @param {string} [params.endTime] 结束时间
 * @param {string} [params.module] 功能模块
 * @param {number} [params.status] 操作状态
 * @returns {Promise} 导出响应
 */
export const exportSystemLog = (params = {}) => {
  return http.download(PORT1 + `/system/log/export`, params, {
    loading: true,
    responseType: 'blob'
  });
};

// ==================== 系统日志批量操作接口 ====================

/**
 * @description 批量删除系统日志
 * @param {Array<number>} ids 日志ID数组
 * @returns {Promise} 删除响应
 */
export const batchDeleteSystemLog = (ids) => {
  return http.delete(PORT1 + `/system/log/batch`, { ids });
};

/**
 * @description 批量标记日志状态
 * @param {Object} params 批量操作参数
 * @param {Array<number>} params.ids 日志ID数组
 * @param {number} params.status 目标状态
 * @returns {Promise} 操作响应
 */
export const batchUpdateSystemLogStatus = (params) => {
  return http.put(PORT1 + `/system/log/batch/status`, params);
};

// ==================== 系统日志高级查询接口 ====================

/**
 * @description 高级搜索系统日志
 * @param {Object} params 高级搜索参数
 * @param {Array<string>} [params.modules] 模块列表
 * @param {Array<string>} [params.operations] 操作列表
 * @param {Array<string>} [params.ips] IP地址列表
 * @param {Array<number>} [params.userIds] 用户ID列表
 * @param {string} [params.timeRange] 时间范围（today/week/month/quarter/year）
 * @param {Object} [params.customTimeRange] 自定义时间范围
 * @param {string} [params.customTimeRange.start] 开始时间
 * @param {string} [params.customTimeRange.end] 结束时间
 * @returns {Promise} 搜索响应
 */
export const advancedSearchSystemLog = (params) => {
  return http.post(PORT1 + `/system/log/advanced-search`, params);
};

/**
 * @description 获取日志趋势分析
 * @param {Object} params 分析参数
 * @param {string} [params.timeRange='week'] 时间范围
 * @param {string} [params.groupBy='day'] 分组方式（hour/day/week/month）
 * @param {Array<string>} [params.modules] 指定模块
 * @returns {Promise} 趋势分析响应
 */
export const getSystemLogTrend = (params = {}) => {
  return http.get(PORT1 + `/system/log/trend`, params);
};

/**
 * @description 获取热门操作排行
 * @param {Object} params 查询参数
 * @param {number} [params.days=7] 统计天数
 * @param {number} [params.limit=10] 返回数量
 * @param {string} [params.type='operation'] 排行类型（operation/module/user/ip）
 * @returns {Promise} 排行响应
 */
export const getSystemLogTopRanking = (params = {}) => {
  return http.get(PORT1 + `/system/log/ranking`, params);
};
