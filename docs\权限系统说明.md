# 权限系统说明

## 概述

本项目采用基于权限等级的按钮权限控制系统，通过预定义的权限等级来简化权限管理。

## 权限等级

### 🔑 Root权限 (root)
- **描述**: 超级管理员，拥有所有权限
- **权限**: `add`, `edit`, `delete`, `import`, `export`, `batchAdd`, `batchDelete`, `status`, `view`, `upload`

### 👨‍💼 Admin权限 (admin)  
- **描述**: 管理员，无删除和上传权限
- **权限**: `add`, `edit`, `import`, `export`, `batchAdd`, `status`, `view`

### 👤 Operator权限 (operator)
- **描述**: 操作员，只能查看和导出
- **权限**: `view`, `export`

## 后端接口

### 获取用户权限等级
```
GET /jnl_/auth/buttons
```

**返回格式:**
```json
{
  "code": 200,
  "data": {
    "permission": "root"
  },
  "msg": "获取权限等级成功"
}
```

**permission字段可选值:**
- `"root"` - Root权限
- `"admin"` - Admin权限  
- `"operator"` - Operator权限

## 前端使用

### 1. 使用 v-auth 指令

```vue
<!-- 单个权限 -->
<el-button v-auth="'add'" type="primary">新增</el-button>
<el-button v-auth="'edit'" type="warning">编辑</el-button>
<el-button v-auth="'delete'" type="danger">删除</el-button>

<!-- 多个权限（需要同时拥有所有权限才显示） -->
<el-button v-auth="['add', 'edit']" type="primary">批量操作</el-button>
```

### 2. 使用 useAuthButtons Hook

```javascript
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS, permissionLevel, permissions } = useAuthButtons();

// 在模板中使用
<el-button v-if="BUTTONS.add" type="primary">新增</el-button>
```

### 3. 手动权限检查

```javascript
import { hasPermission } from "@/config/permissions";
import { useAuthStore } from "@/stores/modules/auth";

const authStore = useAuthStore();
const canDelete = hasPermission(authStore.permissionLevelGet, 'delete');
```

## 配置文件

权限配置位于 `src/config/permissions.js`，可以根据需要修改权限等级和对应的权限列表。

## 本地测试

如需使用本地数据进行测试，可以修改 `src/assets/json/permissionLevel.json` 文件中的权限等级：

```json
{
  "code": 200,
  "data": {
    "permission": "admin"
  },
  "msg": "获取权限等级成功"
}
```

然后在 `src/api/modules/login.js` 中启用本地数据：

```javascript
export const getAuthButtonListApi = () => {
  // return http.get(PORT1 + `/auth/buttons`, {}, { loading: false });
  // 启用本地数据
  return permissionLevel;
};
```
