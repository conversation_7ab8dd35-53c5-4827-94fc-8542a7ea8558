<template>
  <div class="dashboard-container">
    <!-- 顶部导航栏 -->
    <header class="dashboard-header">
      <div class="header-left">
        <h1 class="dashboard-title">
          <i class="icon-store"></i>
          wujin-jnl 商城数据中心
        </h1>
      </div>
      <div class="header-right">
        <button class="nav-btn" @click="router.push(HOME_URL)">
          <i class="icon-home"></i>
          返回首页
        </button>
        <button class="nav-btn">
          <i class="icon-download"></i>
          导出报告
        </button>
        <div class="current-time">
          <i class="icon-clock"></i>
          {{ time }}
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="dashboard-main" ref="dataScreenRef">
      <!-- 顶部统计卡片 -->
      <section class="stats-overview">
        <div class="stat-card" v-for="(stat, index) in overviewStats" :key="index">
          <div class="stat-icon" :style="{ backgroundColor: stat.color }">
            <i :class="stat.icon"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
            <div class="stat-trend" :class="stat.trend > 0 ? 'positive' : 'negative'">
              <i :class="stat.trend > 0 ? 'icon-arrow-up' : 'icon-arrow-down'"></i>
              {{ Math.abs(stat.trend) }}%
            </div>
          </div>
        </div>
      </section>

      <!-- 主要图表区域 -->
      <section class="main-charts">
        <!-- 第一行：主要数据图表 -->
        <div class="charts-row primary-row">
          <div class="chart-card large">
            <div class="card-header">
              <h3 class="card-title">
                <i class="icon-chart-line"></i>
                近15日成交趋势
              </h3>
            </div>
            <div class="card-content">
              <RevenueChart />
            </div>
          </div>

          <div class="chart-card large">
            <div class="card-header">
              <h3 class="card-title">
                <i class="icon-chart-bar"></i>
                访问量趋势分析
              </h3>
            </div>
            <div class="card-content">
              <VisitorChart />
            </div>
          </div>
        </div>

        <!-- 第二行：分析图表 -->
        <div class="charts-row secondary-row">
          <div class="chart-card medium">
            <div class="card-header">
              <h3 class="card-title">
                <i class="icon-chart-pie"></i>
                交易统计分析
              </h3>
            </div>
            <div class="card-content">
              <TransactionChart />
            </div>
          </div>

          <div class="chart-card medium">
            <div class="card-header">
              <h3 class="card-title">
                <i class="icon-chart-donut"></i>
                产品销售占比
              </h3>
            </div>
            <div class="card-content">
              <ProductSalesChart />
            </div>
          </div>

          <div class="chart-card medium">
            <div class="card-header">
              <h3 class="card-title">
                <i class="icon-trophy"></i>
                热销产品排行
              </h3>
            </div>
            <div class="card-content">
              <HotProductsChart />
            </div>
          </div>
        </div>

        <!-- 第三行：状态监控 -->
        <div class="charts-row status-row">
          <div class="chart-card small">
            <div class="card-header">
              <h3 class="card-title">
                <i class="icon-warning"></i>
                库存预警
              </h3>
            </div>
            <div class="card-content">
              <div class="inventory-alerts">
                <div class="alert-item" v-for="item in inventoryWarnings" :key="item.id">
                  <div class="alert-icon" :class="item.level">
                    <i class="icon-alert"></i>
                  </div>
                  <div class="alert-content">
                    <div class="product-name">{{ item.productName }}</div>
                    <div class="stock-info">剩余 {{ item.stock }} 件</div>
                  </div>
                  <div class="alert-badge" :class="item.level">
                    {{ item.levelText }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="chart-card small">
            <div class="card-header">
              <h3 class="card-title">
                <i class="icon-package"></i>
                订单状态分布
              </h3>
            </div>
            <div class="card-content">
              <div class="order-status-grid">
                <div class="status-item" v-for="status in orderStatus" :key="status.name">
                  <div class="status-icon" :style="{ backgroundColor: status.color }">
                    <i :class="status.icon"></i>
                  </div>
                  <div class="status-info">
                    <div class="status-count">{{ status.count }}</div>
                    <div class="status-name">{{ status.name }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup name="dataScreen">
import { ref, onMounted, onBeforeUnmount } from "vue";
import { HOME_URL } from "@/config";
import { useRouter } from "vue-router";
import TransactionChart from "./components/TransactionChart.vue";
import RevenueChart from "./components/RevenueChart.vue";
import VisitorChart from "./components/VisitorChart.vue";
import ProductSalesChart from "./components/ProductSalesChart.vue";
import HotProductsChart from "./components/HotProductsChart.vue";
import { getRealTimeStatsApi } from "@/api/modules/dataScreen";
import dayjs from "dayjs";

const router = useRouter();
const dataScreenRef = ref(null);

// 顶部概览统计数据
const overviewStats = ref([
  {
    label: "今日销售额",
    value: "¥89,650",
    icon: "icon-money",
    color: "#1890ff",
    trend: 12.5
  },
  {
    label: "今日订单",
    value: "156",
    icon: "icon-shopping-cart",
    color: "#52c41a",
    trend: 8.2
  },
  {
    label: "在线用户",
    value: "234",
    icon: "icon-users",
    color: "#722ed1",
    trend: -2.1
  },
  {
    label: "商品总数",
    value: "1,567",
    icon: "icon-package",
    color: "#fa8c16",
    trend: 5.8
  }
]);

// 库存预警数据
const inventoryWarnings = ref([
  { id: 1, productName: "高强度从动轮", stock: 5, level: "danger", levelText: "紧急" },
  { id: 2, productName: "精密主动轮", stock: 12, level: "warning", levelText: "警告" },
  { id: 3, productName: "不锈钢螺丝", stock: 8, level: "danger", levelText: "紧急" },
  { id: 4, productName: "耐磨胶轮", stock: 15, level: "warning", levelText: "警告" },
  { id: 5, productName: "合金铁轮", stock: 3, level: "danger", levelText: "紧急" }
]);

// 订单状态统计
const orderStatus = ref([
  { name: "待付款", count: 45, color: "#ffa726", icon: "icon-clock" },
  { name: "待发货", count: 78, color: "#42a5f5", icon: "icon-package" },
  { name: "已发货", count: 123, color: "#66bb6a", icon: "icon-truck" },
  { name: "已完成", count: 234, color: "#26c6da", icon: "icon-check-circle" }
]);

// 获取实时统计数据
const getRealTimeStats = async () => {
  try {
    // 保留API调用接口，但暂时使用模拟数据
    // const { data } = await getRealTimeStatsApi();
    console.log("获取实时统计数据:", overviewStats.value);
  } catch (error) {
    console.error("获取实时统计数据失败:", error);
  }
};

onMounted(() => {
  getRealTimeStats();

  if (dataScreenRef.value) {
    dataScreenRef.value.style.transform = `scale(${getScale()}) translate(-50%, -50%)`;
    dataScreenRef.value.style.width = `1920px`;
    dataScreenRef.value.style.height = `1080px`;
  }
  window.addEventListener("resize", resize);
});

// 设置响应式
const resize = () => {
  if (dataScreenRef.value) {
    dataScreenRef.value.style.transform = `scale(${getScale()}) translate(-50%, -50%)`;
  }
};

// 根据浏览器大小推断缩放比例
const getScale = (width = 1920, height = 1080) => {
  let ww = window.innerWidth / width;
  let wh = window.innerHeight / height;
  return ww < wh ? ww : wh;
};

// 获取当前时间
let timer = null;
let time = ref(dayjs().format("YYYY年MM月DD HH:mm:ss"));
timer = setInterval(() => {
  time.value = dayjs().format("YYYY年MM月DD HH:mm:ss");
}, 1000);

onBeforeUnmount(() => {
  window.removeEventListener("resize", resize);
  clearInterval(timer);
});
</script>
<style lang="scss" scoped>
// 现代化商城数据大屏样式

// 图标字体定义
.icon-store::before { content: "🏪"; }
.icon-home::before { content: "🏠"; }
.icon-download::before { content: "📥"; }
.icon-clock::before { content: "🕐"; }
.icon-money::before { content: "💰"; }
.icon-shopping-cart::before { content: "🛒"; }
.icon-users::before { content: "👥"; }
.icon-package::before { content: "📦"; }
.icon-arrow-up::before { content: "↗️"; }
.icon-arrow-down::before { content: "↘️"; }
.icon-chart-pie::before { content: "📊"; }
.icon-chart-line::before { content: "📈"; }
.icon-chart-donut::before { content: "🍩"; }
.icon-chart-bar::before { content: "📊"; }
.icon-trophy::before { content: "🏆"; }
.icon-warning::before { content: "⚠️"; }
.icon-alert::before { content: "🚨"; }
.icon-truck::before { content: "🚚"; }
.icon-check-circle::before { content: "✅"; }
.icon-refresh::before { content: "🔄"; }

// 主容器
.dashboard-container {
  width: 100vw;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  color: #333;
  overflow-x: hidden;
  box-sizing: border-box;
}

// 顶部导航栏
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;

  .header-left {
    .dashboard-title {
      font-size: 24px;
      font-weight: 700;
      color: #2c3e50;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 12px;

      i {
        font-size: 28px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 20px;

    .nav-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 10px 20px;
      background: linear-gradient(45deg, #667eea, #764ba2);
      color: white;
      border: none;
      border-radius: 25px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }

      i {
        font-size: 16px;
      }
    }

    .current-time {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 10px 16px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 20px;
      font-weight: 500;
      color: #2c3e50;
      border: 1px solid rgba(102, 126, 234, 0.2);

      i {
        font-size: 16px;
        color: #667eea;
      }
    }
  }
}

// 主要内容区域
.dashboard-main {
  padding: 20px 30px;
  width: 100%;
  box-sizing: border-box;
}

// 顶部统计卡片区域
.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 30px;

  .stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea, #764ba2);
    }

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }

    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .stat-content {
      flex: 1;

      .stat-value {
        font-size: 28px;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 14px;
        color: #7f8c8d;
        margin-bottom: 8px;
      }

      .stat-trend {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        font-weight: 600;

        &.positive {
          color: #27ae60;
        }

        &.negative {
          color: #e74c3c;
        }

        i {
          font-size: 14px;
        }
      }
    }
  }
}

// 主要图表区域布局
.main-charts {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .charts-row {
    display: grid;
    gap: 24px;

    &.primary-row {
      grid-template-columns: 1fr 1fr;
      margin-bottom: 8px;
    }

    &.secondary-row {
      grid-template-columns: repeat(3, 1fr);
      margin-bottom: 8px;
    }

    &.status-row {
      grid-template-columns: 1fr 1fr;
    }
  }

  .chart-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    overflow: hidden;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }

    &.large {
      min-height: 350px;
    }

    &.medium {
      min-height: 300px;
    }

    &.small {
      min-height: 250px;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      background: linear-gradient(90deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));

      .card-title {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 15px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;

        i {
          font-size: 16px;
          color: #667eea;
        }
      }

      .card-actions {
        .action-btn {
          width: 28px;
          height: 28px;
          border: none;
          background: rgba(102, 126, 234, 0.1);
          border-radius: 6px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: rotate(180deg);
          }

          i {
            font-size: 12px;
            color: #667eea;
          }
        }
      }
    }

    .card-content {
      padding: 16px;
      height: calc(100% - 60px);
      width: 100%;
      box-sizing: border-box;

      // 确保图表组件填充整个容器
      > div {
        width: 100% !important;
        height: 100% !important;
      }
    }
  }
}

// 库存预警样式
.inventory-alerts {
  .alert-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    margin-bottom: 12px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 12px;
    border-left: 4px solid transparent;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.05);
      transform: translateX(4px);
    }

    .alert-icon {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;

      &.danger {
        background: rgba(231, 76, 60, 0.1);
        color: #e74c3c;
        border-left-color: #e74c3c;
      }

      &.warning {
        background: rgba(255, 167, 38, 0.1);
        color: #f39c12;
        border-left-color: #f39c12;
      }
    }

    .alert-content {
      flex: 1;

      .product-name {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 4px;
      }

      .stock-info {
        font-size: 12px;
        color: #7f8c8d;
      }
    }

    .alert-badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 11px;
      font-weight: 600;

      &.danger {
        background: #e74c3c;
        color: white;
      }

      &.warning {
        background: #f39c12;
        color: white;
      }
    }
  }
}

// 订单状态网格
.order-status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;

  .status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 12px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.05);
      transform: scale(1.02);
    }

    .status-icon {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 16px;
    }

    .status-info {
      .status-count {
        font-size: 20px;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 2px;
      }

      .status-name {
        font-size: 12px;
        color: #7f8c8d;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1400px) {
  .main-charts {
    .charts-row {
      &.secondary-row {
        grid-template-columns: 1fr 1fr;

        .chart-card:last-child {
          grid-column: 1 / -1;
        }
      }
    }
  }
}

@media (max-width: 1024px) {
  .main-charts {
    .charts-row {
      &.primary-row {
        grid-template-columns: 1fr;
      }

      &.secondary-row {
        grid-template-columns: 1fr;
      }

      &.status-row {
        grid-template-columns: 1fr;
      }
    }
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    padding: 16px 20px;

    .header-right {
      flex-wrap: wrap;
      justify-content: center;
    }
  }

  .dashboard-main {
    padding: 20px;
  }

  .stats-overview {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 16px;
  }

  .main-charts {
    gap: 16px;

    .charts-row {
      gap: 16px;
    }

    .chart-card {
      &.large, &.medium, &.small {
        min-height: 280px;
      }
    }
  }

  .order-status-grid {
    grid-template-columns: 1fr;
  }
}
</style>
