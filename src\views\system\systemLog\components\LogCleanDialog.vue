<template>
  <el-dialog
    v-model="dialogVisible"
    title="清理日志"
    width="500px"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <el-form :model="cleanForm" :rules="rules" ref="formRef" label-width="120px">
      <el-alert
        title="注意事项"
        type="warning"
        :closable="false"
        show-icon
        class="mb20"
      >
        <template #default>
          <div>
            <p>• 清理操作不可逆，请谨慎操作</p>
            <p>• 建议定期清理过期日志以释放存储空间</p>
            <p>• 最小保留天数为7天，确保系统正常运行</p>
          </div>
        </template>
      </el-alert>
      
      <el-form-item label="保留天数" prop="days">
        <el-input-number
          v-model="cleanForm.days"
          :min="7"
          :max="365"
          :step="1"
          placeholder="请输入保留天数"
          style="width: 100%"
        />
        <div class="form-tip">
          超过 {{ cleanForm.days }} 天的日志将被删除
        </div>
      </el-form-item>
      
      <el-form-item label="预计清理">
        <div class="clean-preview">
          <el-button type="primary" plain @click="previewClean" :loading="previewLoading">
            预览清理结果
          </el-button>
          <div v-if="previewResult.total > 0" class="preview-result">
            <el-tag type="warning" size="large">
              预计删除 {{ previewResult.total }} 条日志记录
            </el-tag>
          </div>
        </div>
      </el-form-item>
      
      <el-form-item label="确认操作" prop="confirm">
        <el-checkbox v-model="cleanForm.confirm">
          我已了解清理操作的风险，确认执行清理
        </el-checkbox>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button 
          type="danger" 
          @click="executeClean" 
          :loading="cleanLoading"
          :disabled="!cleanForm.confirm || previewResult.total === 0"
        >
          执行清理
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { cleanExpiredSystemLog, getSystemLogPage } from "@/api/modules/systemLog";

// 定义事件
const emit = defineEmits(['refresh']);

// 对话框可见性
const dialogVisible = ref(false);

// 表单引用
const formRef = ref(null);

// 加载状态
const previewLoading = ref(false);
const cleanLoading = ref(false);

// 清理表单
const cleanForm = reactive({
  days: 30,
  confirm: false
});

// 预览结果
const previewResult = reactive({
  total: 0,
  oldestDate: '',
  newestDate: ''
});

// 表单验证规则
const rules = {
  days: [
    { required: true, message: '请输入保留天数', trigger: 'blur' },
    { type: 'number', min: 7, max: 365, message: '保留天数必须在7-365天之间', trigger: 'blur' }
  ],
  confirm: [
    { 
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请确认清理操作'));
        } else {
          callback();
        }
      }, 
      trigger: 'change' 
    }
  ]
};

/**
 * 预览清理结果
 */
const previewClean = async () => {
  try {
    previewLoading.value = true;
    
    // 计算截止日期
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - cleanForm.days);
    const cutoffDateStr = cutoffDate.toISOString().split('T')[0] + ' 23:59:59';
    
    // 查询要删除的日志数量
    const { data } = await getSystemLogPage({
      current: 1,
      size: 1,
      endTime: cutoffDateStr
    });
    
    previewResult.total = data.total || 0;
    
    if (previewResult.total > 0) {
      ElMessage.success(`预览完成，将删除 ${previewResult.total} 条日志记录`);
    } else {
      ElMessage.info('没有需要清理的日志记录');
    }
  } catch (error) {
    ElMessage.error('预览失败，请重试');
  } finally {
    previewLoading.value = false;
  }
};

/**
 * 执行清理操作
 */
const executeClean = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) return;
    
    // 二次确认
    await ElMessageBox.confirm(
      `确定要删除超过 ${cleanForm.days} 天的 ${previewResult.total} 条日志记录吗？此操作不可逆！`,
      "清理确认",
      {
        confirmButtonText: "确定清理",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true
      }
    );
    
    cleanLoading.value = true;
    
    // 执行清理
    const { data } = await cleanExpiredSystemLog({ days: cleanForm.days });
    
    ElMessage.success(`清理完成，共删除 ${data} 条日志记录`);
    
    // 关闭对话框
    dialogVisible.value = false;
    
    // 通知父组件刷新
    emit('refresh');
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清理失败，请重试');
    }
  } finally {
    cleanLoading.value = false;
  }
};

/**
 * 重置表单
 */
const resetForm = () => {
  cleanForm.days = 30;
  cleanForm.confirm = false;
  previewResult.total = 0;
  previewResult.oldestDate = '';
  previewResult.newestDate = '';
  formRef.value?.clearValidate();
};

/**
 * 打开对话框
 */
const open = () => {
  resetForm();
  dialogVisible.value = true;
};

// 暴露方法给父组件
defineExpose({
  open
});
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.clean-preview {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.preview-result {
  display: flex;
  align-items: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
