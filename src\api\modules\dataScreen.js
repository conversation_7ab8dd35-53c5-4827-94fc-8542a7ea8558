import http from "@/api";

const PORT1 = "/jnl_api";

/**
 * @name 数据大屏模块
 */

// 获取交易统计数据 (成交量、退款量、退货量)
export const getTransactionDataApi = () => {
  return http.get(PORT1 + `/dataScreen/transaction`);
};

// 获取近15日成交金额数据
export const getRevenueDataApi = () => {
  return http.get(PORT1 + `/dataScreen/revenue`);
};

// 获取近15日商城访问量数据
export const getVisitorDataApi = () => {
  return http.get(PORT1 + `/dataScreen/visitor`);
};

// 获取产品销售比例数据
export const getProductSalesDataApi = () => {
  return http.get(PORT1 + `/dataScreen/productSales`);
};

// 获取实时统计数据
export const getRealTimeStatsApi = () => {
  return http.get(PORT1 + `/dataScreen/realTimeStats`);
};

// 获取热销产品数据
export const getHotProductsDataApi = () => {
  return http.get(PORT1 + `/dataScreen/hotProducts`);
};
