/**
 * 图片优化工具函数
 */

// 图片缓存
const imageCache = new Map();
const loadingPromises = new Map();

/**
 * 检查图片是否已缓存
 * @param {string} url 图片URL
 * @returns {boolean}
 */
export const isImageCached = (url) => {
  return imageCache.has(url);
};

/**
 * 预加载图片
 * @param {string} url 图片URL
 * @returns {Promise}
 */
export const preloadImage = (url) => {
  if (!url) return Promise.reject(new Error('URL is required'));
  
  // 如果已缓存，直接返回
  if (isImageCached(url)) {
    return Promise.resolve(url);
  }
  
  // 如果正在加载，返回现有的Promise
  if (loadingPromises.has(url)) {
    return loadingPromises.get(url);
  }
  
  const promise = new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      imageCache.set(url, {
        loaded: true,
        timestamp: Date.now(),
        width: img.naturalWidth,
        height: img.naturalHeight
      });
      loadingPromises.delete(url);
      resolve(url);
    };
    
    img.onerror = () => {
      loadingPromises.delete(url);
      reject(new Error(`Failed to load image: ${url}`));
    };
    
    img.src = url;
  });
  
  loadingPromises.set(url, promise);
  return promise;
};

/**
 * 批量预加载图片
 * @param {string[]} urls 图片URL数组
 * @param {number} concurrency 并发数量
 * @returns {Promise}
 */
export const preloadImages = async (urls, concurrency = 3) => {
  const results = [];
  const executing = [];
  
  for (const url of urls) {
    const promise = preloadImage(url).then(
      (result) => ({ status: 'fulfilled', value: result }),
      (error) => ({ status: 'rejected', reason: error })
    );
    
    results.push(promise);
    
    if (urls.length >= concurrency) {
      executing.push(promise);
      
      if (executing.length >= concurrency) {
        await Promise.race(executing);
        executing.splice(executing.findIndex(p => p === promise), 1);
      }
    }
  }
  
  return Promise.all(results);
};

/**
 * 生成缩略图URL
 * @param {string} url 原图URL
 * @param {object} options 选项
 * @returns {string}
 */
export const generateThumbnailUrl = (url, options = {}) => {
  if (!url) return '';
  
  const {
    width = 100,
    height = 100,
    quality = 80,
    format = 'webp'
  } = options;
  
  // 根据不同的CDN服务调整参数格式
  // 这里以阿里云OSS为例，实际使用时需要根据具体CDN调整
  if (url.includes('aliyuncs.com')) {
    return `${url}?x-oss-process=image/resize,w_${width},h_${height}/quality,q_${quality}/format,${format}`;
  }
  
  // 腾讯云COS
  if (url.includes('myqcloud.com')) {
    return `${url}?imageView2/1/w/${width}/h/${height}/q/${quality}/format/${format}`;
  }
  
  // 七牛云
  if (url.includes('qiniudn.com') || url.includes('clouddn.com')) {
    return `${url}?imageView2/1/w/${width}/h/${height}/q/${quality}/format/${format}`;
  }
  
  // 通用参数格式
  const separator = url.includes('?') ? '&' : '?';
  return `${url}${separator}w=${width}&h=${height}&q=${quality}&f=${format}`;
};

/**
 * 清理过期缓存
 * @param {number} maxAge 最大缓存时间（毫秒）
 */
export const clearExpiredCache = (maxAge = 30 * 60 * 1000) => { // 默认30分钟
  const now = Date.now();
  const expiredKeys = [];
  
  imageCache.forEach((value, key) => {
    if (now - value.timestamp > maxAge) {
      expiredKeys.push(key);
    }
  });
  
  expiredKeys.forEach(key => {
    imageCache.delete(key);
  });
  
  console.log(`清理了 ${expiredKeys.length} 个过期图片缓存`);
};

/**
 * 获取缓存统计信息
 * @returns {object}
 */
export const getCacheStats = () => {
  return {
    size: imageCache.size,
    loadingCount: loadingPromises.size,
    cacheKeys: Array.from(imageCache.keys())
  };
};

/**
 * 清空所有缓存
 */
export const clearAllCache = () => {
  imageCache.clear();
  loadingPromises.clear();
  console.log('已清空所有图片缓存');
};

/**
 * 图片懒加载观察器
 */
export class LazyImageObserver {
  constructor(options = {}) {
    this.options = {
      rootMargin: '50px',
      threshold: 0.1,
      ...options
    };
    
    this.observer = null;
    this.callbacks = new Map();
    
    this.init();
  }
  
  init() {
    if (!window.IntersectionObserver) {
      console.warn('IntersectionObserver not supported');
      return;
    }
    
    this.observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const callback = this.callbacks.get(entry.target);
          if (callback) {
            callback();
            this.unobserve(entry.target);
          }
        }
      });
    }, this.options);
  }
  
  observe(element, callback) {
    if (!this.observer) {
      // 如果不支持IntersectionObserver，直接执行回调
      callback();
      return;
    }
    
    this.callbacks.set(element, callback);
    this.observer.observe(element);
  }
  
  unobserve(element) {
    if (this.observer) {
      this.observer.unobserve(element);
    }
    this.callbacks.delete(element);
  }
  
  disconnect() {
    if (this.observer) {
      this.observer.disconnect();
    }
    this.callbacks.clear();
  }
}

// 创建全局懒加载观察器实例
export const globalLazyObserver = new LazyImageObserver();

// 定期清理过期缓存
if (typeof window !== 'undefined') {
  setInterval(() => {
    clearExpiredCache();
  }, 10 * 60 * 1000); // 每10分钟清理一次
}
