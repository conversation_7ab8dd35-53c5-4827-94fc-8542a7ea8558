# ProductTbl 商品导入导出 API 文档

## 概述

本文档描述了JNL-Shop系统中商品信息的导入导出功能API，包括模板下载、数据导出和批量导入功能。

## 基础信息

- **服务名称**: ProductTblService
- **控制器**: ProductTblController
- **基础路径**: `/api/products`
- **版本**: v1.0
- **认证方式**: JWT Token

---

## API 接口列表

### 1. 导出商品模板/数据

#### 接口信息
- **接口名称**: 导出商品模板或数据
- **请求方法**: `GET`
- **请求路径**: `/api/products/export`
- **功能描述**: 导出商品导入模板或现有商品数据到Excel文件

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 描述 | 示例值 |
|--------|------|------|--------|------|--------|
| type | String | 否 | template | 导出类型 | template 或 data |

**type参数说明**:
- `template`: 导出空白模板，用于批量导入
- `data`: 导出现有商品数据

#### 请求示例

```bash
# 导出模板
GET /api/products/export?type=template

# 导出数据
GET /api/products/export?type=data
```

#### 响应信息

**成功响应**:
- **状态码**: 200
- **Content-Type**: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- **响应体**: Excel文件二进制数据

**响应头**:
```
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
Content-Disposition: attachment; filename="product_template.xlsx" 或 "product_data.xlsx"
```

**错误响应**:
```json
{
  "code": 500,
  "message": "导出失败：具体错误信息",
  "data": null
}
```

#### Excel文件格式

**表头字段**（按顺序）:

| 列序号 | 字段名 | 是否必填 | 数据类型 | 说明 |
|--------|--------|----------|----------|------|
| A | 产品编码* | 是 | String | 商品唯一编码，不可重复 |
| B | 产品名称* | 是 | String | 商品名称 |
| C | 分类名称* | 是 | String | 必须是叶子节点分类 |
| D | 条码 | 否 | String | 商品条码 |
| E | 品牌 | 否 | String | 商品品牌 |
| F | SKU | 否 | String | 商品SKU编码 |
| G | 状态(0-下架,1-上架) | 否 | Integer | 0或1，默认1 |
| H | 标准售价 | 否 | Double | 商品标准价格 |
| I | 优惠价格1 | 否 | Double | 第一档优惠价格 |
| J | 优惠价格2 | 否 | Double | 第二档优惠价格 |
| K | 优惠价格3 | 否 | Double | 第三档优惠价格 |
| L | 优惠价格4 | 否 | Double | 第四档优惠价格 |
| M | 优惠价格5 | 否 | Double | 第五档优惠价格 |
| N | 库存 | 否 | Integer | 商品库存数量，默认0 |
| O | 描述 | 否 | String | 商品描述信息 |

---

### 2. 导入商品数据

#### 接口信息
- **接口名称**: 批量导入商品数据
- **请求方法**: `POST`
- **请求路径**: `/api/products/import`
- **功能描述**: 通过Excel文件批量导入商品信息
- **Content-Type**: `multipart/form-data`

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 限制 |
|--------|------|------|------|------|
| file | MultipartFile | 是 | Excel文件 | .xlsx或.xls格式 |

#### 请求示例

```bash
curl -X POST \
  http://localhost:8080/api/products/import \
  -H 'Authorization: Bearer {token}' \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@products.xlsx'
```

#### 响应信息

**成功响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalRows": 100,
    "successCount": 95,
    "errorCount": 5,
    "errorRecords": [
      {
        "rowNum": 3,
        "errors": ["产品编码不能为空"]
      },
      {
        "rowNum": 15,
        "errors": ["分类名称不存在: 不存在的分类"]
      },
      {
        "rowNum": 28,
        "errors": ["商品编码已存在: PROD001"]
      }
    ]
  }
}
```

**响应字段说明**:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| totalRows | Integer | 总处理行数（不包括表头） |
| successCount | Integer | 成功导入的商品数量 |
| errorCount | Integer | 导入失败的商品数量 |
| errorRecords | Array | 错误记录详情 |
| errorRecords[].rowNum | Integer | 错误行号（从1开始，包括表头） |
| errorRecords[].errors | Array | 该行的错误信息列表 |

**错误响应**:
```json
{
  "code": 400,
  "message": "上传文件不能为空",
  "data": null
}
```

```json
{
  "code": 400,
  "message": "请上传Excel文件",
  "data": null
}
```

```json
{
  "code": 500,
  "message": "导入失败：Excel文件格式不正确，缺少表头",
  "data": null
}
```

---

## 数据验证规则

### 必填字段验证
- **产品编码**: 不能为空，不能重复
- **产品名称**: 不能为空
- **分类名称**: 不能为空，必须存在且为叶子节点

### 数据格式验证
- **状态**: 只能是0（下架）或1（上架）
- **价格字段**: 必须是有效的数字格式
- **库存**: 必须是非负整数
- **分类验证**: 分类必须存在于系统中且为叶子节点

### 业务规则验证
- 商品编码在系统中必须唯一
- 分类必须是叶子节点（不能选择父级分类）
- 价格不能为负数
- 库存不能为负数

---

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | 上传文件不能为空 | 未选择文件或文件为空 |
| 400 | 请上传Excel文件 | 文件格式不正确 |
| 400 | Excel文件表头格式不正确 | 表头字段不匹配模板 |
| 500 | 导出失败 | 服务器内部错误 |
| 500 | 导入失败 | 文件处理异常 |

---

## 使用示例

### 1. 下载导入模板

```javascript
// 前端下载模板示例
function downloadTemplate() {
  const link = document.createElement('a');
  link.href = '/api/products/export?type=template';
  link.download = 'product_template.xlsx';
  link.click();
}
```

### 2. 导入商品数据

```javascript
// 前端上传文件示例
function importProducts(file) {
  const formData = new FormData();
  formData.append('file', file);
  
  fetch('/api/products/import', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ' + token
    },
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    if (data.code === 200) {
      console.log('导入成功:', data.data);
      // 处理导入结果
      if (data.data.errorCount > 0) {
        console.log('部分导入失败:', data.data.errorRecords);
      }
    } else {
      console.error('导入失败:', data.message);
    }
  });
}
```

### 3. 导出现有数据

```bash
# 使用curl导出数据
curl -X GET \
  "http://localhost:8080/api/products/export?type=data" \
  -H "Authorization: Bearer {token}" \
  -o products_data.xlsx
```

---

## 注意事项

1. **文件大小限制**: 建议单次导入不超过1000条记录
2. **并发限制**: 同一用户同时只能进行一个导入操作
3. **事务处理**: 导入过程中如果部分记录失败，成功的记录仍会保存
4. **缓存更新**: 导入成功后会自动清除相关缓存
5. **日志记录**: 所有导入导出操作都会记录到系统日志
6. **权限要求**: 需要商品管理相关权限

---

## 数据模型

### ProductTbl 商品实体

```json
{
  "pid": 1,
  "productCode": "PROD001",
  "productName": "示例商品",
  "barCode": "1234567890123",
  "brand": "示例品牌",
  "hierarchy": 101,
  "typeName": "电子产品",
  "sku": "SKU001",
  "status": 1,
  "price": 99.99,
  "priceOne": 89.99,
  "priceTwo": 79.99,
  "priceThree": 69.99,
  "priceFour": 59.99,
  "priceFive": 49.99,
  "inventory": 100,
  "describe": "这是一个示例商品",
  "createTime": "2025-07-23 10:00:00",
  "updateTime": "2025-07-23 10:00:00"
}
```

### 字段详细说明

| 字段名 | 数据库字段 | 类型 | 长度 | 约束 | 描述 |
|--------|------------|------|------|------|------|
| pid | pid | Integer | - | 主键，自增 | 商品ID |
| productCode | product_code | String | 50 | 唯一，非空 | 商品编码 |
| productName | product_name | String | 100 | 非空 | 商品名称 |
| barCode | bar_code | String | 50 | - | 商品条码 |
| brand | brand | String | 50 | - | 商品品牌 |
| hierarchy | hierarchy | Integer | - | 外键 | 分类ID |
| typeName | - | String | - | 虚拟字段 | 分类名称 |
| sku | sku | String | 100 | - | SKU编码 |
| status | status | Integer | - | 默认1 | 状态(0-下架,1-上架) |
| price | price | Double | - | - | 标准售价 |
| priceOne | price_one | Double | - | - | 优惠价格1 |
| priceTwo | price_two | Double | - | - | 优惠价格2 |
| priceThree | price_three | Double | - | - | 优惠价格3 |
| priceFour | price_four | Double | - | - | 优惠价格4 |
| priceFive | price_five | Double | - | - | 优惠价格5 |
| inventory | inventory | Integer | - | 默认0 | 库存数量 |
| describe | describe | String | 500 | - | 商品描述 |
| createTime | create_time | String | - | - | 创建时间 |
| updateTime | update_time | String | - | - | 更新时间 |

---

## 高级用法

### 1. 批量导入最佳实践

```java
// 服务端处理大文件导入的建议
@Service
public class ProductImportService {

    @Transactional(rollbackFor = Exception.class)
    public Result<?> importLargeFile(MultipartFile file) {
        // 1. 分批处理，避免内存溢出
        int batchSize = 1000;

        // 2. 使用流式处理
        try (InputStream inputStream = file.getInputStream()) {
            // 处理逻辑
        }

        // 3. 异步处理大文件
        if (totalRows > 5000) {
            return processAsync(file);
        }

        return processSynchronously(file);
    }
}
```

### 2. 错误处理策略

```javascript
// 前端错误处理示例
function handleImportResult(result) {
  const { totalRows, successCount, errorCount, errorRecords } = result.data;

  if (errorCount === 0) {
    // 全部成功
    showSuccess(`成功导入 ${successCount} 条商品记录`);
  } else if (successCount > 0) {
    // 部分成功
    showWarning(`导入完成：成功 ${successCount} 条，失败 ${errorCount} 条`);
    showErrorDetails(errorRecords);
  } else {
    // 全部失败
    showError('导入失败，请检查文件格式和数据');
    showErrorDetails(errorRecords);
  }
}

function showErrorDetails(errorRecords) {
  const errorMessages = errorRecords.map(record =>
    `第 ${record.rowNum} 行: ${record.errors.join(', ')}`
  ).join('\n');

  console.log('详细错误信息:\n', errorMessages);
}
```

### 3. 导入进度监控

```javascript
// 大文件导入进度监控
function importWithProgress(file) {
  const formData = new FormData();
  formData.append('file', file);

  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();

    // 上传进度
    xhr.upload.addEventListener('progress', (e) => {
      if (e.lengthComputable) {
        const percentComplete = (e.loaded / e.total) * 100;
        updateProgressBar(percentComplete);
      }
    });

    xhr.onload = () => {
      if (xhr.status === 200) {
        resolve(JSON.parse(xhr.responseText));
      } else {
        reject(new Error('导入失败'));
      }
    };

    xhr.open('POST', '/api/products/import');
    xhr.setRequestHeader('Authorization', 'Bearer ' + token);
    xhr.send(formData);
  });
}
```

---

## 性能优化建议

### 1. 导入性能优化
- **批量处理**: 建议单次导入不超过1000条记录
- **数据预处理**: 在前端进行基础数据验证
- **异步处理**: 大文件使用异步导入，返回任务ID
- **缓存策略**: 合理使用缓存减少数据库查询

### 2. 导出性能优化
- **分页导出**: 大量数据分批导出
- **字段选择**: 只导出必要字段
- **压缩传输**: 启用gzip压缩
- **缓存结果**: 相同查询条件的结果可以缓存

---

## 安全考虑

### 1. 文件安全
- **文件类型检查**: 严格验证文件扩展名和MIME类型
- **文件大小限制**: 限制上传文件大小，防止DoS攻击
- **病毒扫描**: 对上传文件进行安全扫描
- **临时文件清理**: 及时清理临时文件

### 2. 数据安全
- **权限验证**: 确保用户有相应的导入导出权限
- **数据脱敏**: 导出时对敏感数据进行脱敏处理
- **操作日志**: 记录所有导入导出操作
- **数据备份**: 重要操作前进行数据备份

---

## 故障排除

### 常见问题及解决方案

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 导入失败：表头格式不正确 | Excel表头与模板不匹配 | 重新下载模板，确保表头完全一致 |
| 分类名称不存在 | 填写了不存在的分类名称 | 检查分类名称是否正确，是否为叶子节点 |
| 商品编码重复 | 导入数据中存在重复编码 | 检查Excel中的商品编码，确保唯一性 |
| 文件上传失败 | 文件过大或网络问题 | 减小文件大小或检查网络连接 |
| 内存溢出 | 导入文件过大 | 分批导入或联系管理员增加内存 |

### 调试信息

开启调试模式时，系统会提供更详细的错误信息：

```json
{
  "code": 500,
  "message": "导入失败",
  "data": {
    "debugInfo": {
      "fileName": "products.xlsx",
      "fileSize": "2.5MB",
      "totalRows": 1500,
      "processedRows": 856,
      "failedAtRow": 857,
      "errorDetail": "分类ID查询失败：数据库连接超时"
    }
  }
}
```

---

## 更新日志

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0 | 2025-07-23 | 初始版本，支持基础导入导出功能 |
