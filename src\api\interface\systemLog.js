/**
 * @description 系统日志相关类型定义
 */

/**
 * @description 系统日志实体
 */
export const SystemLogEntity = {
  id: null,           // 日志ID
  userId: null,       // 用户ID
  username: "",       // 用户名
  module: "",         // 功能模块
  operation: "",      // 操作内容
  method: "",         // 方法
  requestUrl: "",     // 请求URL
  requestMethod: "",  // 请求方式
  requestParam: "",   // 请求参数
  responseData: "",   // 响应数据
  ip: "",             // 操作IP
  ipLocation: "",     // IP归属地
  userAgent: "",      // 用户代理
  status: 1,          // 操作状态（1成功 0失败）
  errorMsg: "",       // 错误信息
  createTime: ""      // 创建时间
};

/**
 * @description 系统日志查询参数
 */
export const SystemLogQueryParams = {
  current: 1,           // 页码
  size: 10,             // 每页大小
  userId: null,         // 用户ID
  username: "",         // 用户名（支持模糊查询）
  module: "",           // 功能模块（支持模糊查询）
  operation: "",        // 操作内容（支持模糊查询）
  requestMethod: "",    // 请求方式（GET、POST等）
  ip: "",               // 操作IP（支持模糊查询）
  status: null,         // 操作状态（1成功 0失败）
  startTime: "",        // 开始时间（格式：yyyy-MM-dd HH:mm:ss）
  endTime: "",          // 结束时间（格式：yyyy-MM-dd HH:mm:ss）
  keyword: "",          // 关键字搜索（匹配用户名、模块、操作内容）
  orderBy: "create_time", // 排序字段
  orderDirection: "desc"  // 排序方式（asc或desc）
};

/**
 * @description 系统日志统计信息
 */
export const SystemLogStatistics = {
  totalOperations: 0,     // 总操作数
  successOperations: 0,   // 成功操作数
  failedOperations: 0,    // 失败操作数
  successRate: 0,         // 成功率
  moduleStats: {},        // 模块统计 { "模块名": 数量 }
  dailyStats: {},         // 每日统计 { "日期": 数量 }
  operationTypeStats: {}  // 操作类型统计 { "操作类型": 数量 }
};

/**
 * @description 系统日志高级搜索参数
 */
export const SystemLogAdvancedSearchParams = {
  modules: [],           // 模块列表
  operations: [],        // 操作列表
  ips: [],               // IP地址列表
  userIds: [],           // 用户ID列表
  timeRange: "",         // 时间范围（today/week/month/quarter/year）
  customTimeRange: {     // 自定义时间范围
    start: "",           // 开始时间
    end: ""              // 结束时间
  }
};

/**
 * @description 系统日志趋势分析参数
 */
export const SystemLogTrendParams = {
  timeRange: "week",     // 时间范围
  groupBy: "day",        // 分组方式（hour/day/week/month）
  modules: []            // 指定模块
};

/**
 * @description 系统日志排行参数
 */
export const SystemLogRankingParams = {
  days: 7,               // 统计天数
  limit: 10,             // 返回数量
  type: "operation"      // 排行类型（operation/module/user/ip）
};

/**
 * @description 系统日志清理参数
 */
export const SystemLogCleanParams = {
  days: 30               // 保留天数（超过此天数的日志将被删除）
};

/**
 * @description 系统日志导出参数
 */
export const SystemLogExportParams = {
  format: "excel",       // 导出格式（excel/csv）
  startTime: "",         // 开始时间
  endTime: "",           // 结束时间
  module: "",            // 功能模块
  status: null           // 操作状态
};

/**
 * @description 系统日志批量操作参数
 */
export const SystemLogBatchParams = {
  ids: [],               // 日志ID数组
  status: null           // 目标状态
};

/**
 * @description 系统日志状态常量
 */
export const SystemLogStatus = {
  SUCCESS: 1,            // 成功
  FAILED: 0              // 失败
};

/**
 * @description 系统日志请求方法常量
 */
export const SystemLogRequestMethods = {
  GET: "GET",
  POST: "POST",
  PUT: "PUT",
  DELETE: "DELETE",
  PATCH: "PATCH"
};

/**
 * @description 系统日志时间范围常量
 */
export const SystemLogTimeRanges = {
  TODAY: "today",
  YESTERDAY: "yesterday",
  THIS_WEEK: "week",
  LAST_WEEK: "lastWeek",
  THIS_MONTH: "month",
  LAST_MONTH: "lastMonth",
  THIS_QUARTER: "quarter",
  LAST_QUARTER: "lastQuarter",
  THIS_YEAR: "year",
  CUSTOM: "custom"
};

/**
 * @description 系统日志分组方式常量
 */
export const SystemLogGroupBy = {
  HOUR: "hour",
  DAY: "day",
  WEEK: "week",
  MONTH: "month"
};

/**
 * @description 系统日志排行类型常量
 */
export const SystemLogRankingTypes = {
  OPERATION: "operation",
  MODULE: "module",
  USER: "user",
  IP: "ip"
};

/**
 * @description 系统日志导出格式常量
 */
export const SystemLogExportFormats = {
  EXCEL: "excel",
  CSV: "csv"
};
