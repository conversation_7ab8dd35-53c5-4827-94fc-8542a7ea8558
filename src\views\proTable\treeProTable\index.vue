<template>
  <div class="main-box">
    <!-- 搜索区域 -->
    <div class="search-box">
      <el-card shadow="hover">
        <div class="search-content">
          <el-input
            v-model="searchText"
            placeholder="搜索分类名称..."
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
            style="width: 300px; margin-right: 16px"
          />
          <el-button type="primary" :icon="CirclePlus" @click="openDialog('add')">新增分类</el-button>
          <el-button type="success" :icon="Refresh" @click="refreshTree">刷新</el-button>
          <el-button type="warning" :icon="Download" @click="exportData">导出数据</el-button>
        </div>
      </el-card>
    </div>

    <!-- 树形数据展示区域 -->
    <div class="tree-box">
      <el-card shadow="hover">
        <template #header>
          <div class="card-header">
            <span class="title">产品分类树 ({{ treeData.length }} 个根节点)</span>
            <div class="header-actions">
              <el-button type="text" :icon="Refresh" @click="refreshTree">刷新</el-button>
              <el-button type="text" :icon="Expand" @click="expandAll">展开全部</el-button>
              <el-button type="text" :icon="Fold" @click="collapseAll">收起全部</el-button>
            </div>
          </div>
        </template>

        <!-- 数据加载状态 -->
        <div v-if="formLoading" class="loading-container">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>数据加载中...</span>
        </div>

        <!-- 空数据状态 -->
        <div v-else-if="treeData.length === 0" class="empty-container">
          <el-empty description="暂无分类数据">
            <el-button type="primary" @click="refreshTree">重新加载</el-button>
          </el-empty>
        </div>

        <!-- 树形数据 -->
        <el-tree
          v-else
          ref="treeRef"
          :data="treeData"
          :props="treeProps"
          :filter-node-method="filterNode"
          :expand-on-click-node="false"
          :default-expand-all="false"
          :highlight-current="true"
          node-key="cid"
          draggable
          @node-click="handleNodeClick"
          class="category-tree"
        >
          <template #default="{ data }">
            <div class="tree-node">
              <div class="node-content">
                <span class="node-icon">
                  <el-icon v-if="data.children && data.children.length > 0">
                    <img src="../useTreeFilter/image/dead-tree.svg" alt="folder" style="width: 1em; height: 1em" />
                  </el-icon>
                  <el-icon v-else>
                    <img src="../useTreeFilter/image/yezi.svg" alt="document" style="width: 1em; height: 1em" />
                  </el-icon>
                </span>
                <div class="node-label-container">
                  <span class="node-label" :title="data.cname">{{ data.cname }}</span>
                  <!-- 叶子节点显示idname -->
                  <span v-if="data.idname" class="node-idname" :title="data.idname">{{ data.idname }}</span>
                </div>
                <span class="node-info">
                  <el-tag size="small" type="info">ID: {{ data.cid }}</el-tag>
                  <el-tag size="small" :type="data.idname ? 'success' : 'warning'">
                    {{ data.idname ? "叶子节点" : "分支节点" }}
                  </el-tag>
                </span>
              </div>
              <div class="node-actions">
                <el-button type="text" size="small" :icon="Plus" @click.stop="openDialog('add', data)">添加</el-button>
                <el-button type="text" size="small" :icon="EditPen" @click.stop="openDialog('edit', data)">编辑</el-button>
                <el-button type="text" size="small" :icon="View" @click.stop="openDialog('view', data)">查看</el-button>
                <el-button type="text" size="small" :icon="Delete" @click.stop="handleDelete(data)" style="color: #f56c6c"
                  >删除</el-button
                >
              </div>
            </div>
          </template>
        </el-tree>
      </el-card>
    </div>

    <!-- 分类详情/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" :close-on-click-modal="false">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" v-loading="formLoading">
        <el-form-item label="分类名称" prop="cname">
          <el-input v-model="formData.cname" placeholder="请输入分类名称" :disabled="dialogType === 'view'" />
        </el-form-item>
        <el-form-item label="标识名称" prop="idname" v-if="dialogType !== 'add' || (dialogType === 'add' && parentData)">
          <el-input v-model="formData.idname" placeholder="请输入标识名称（叶子节点必填）" :disabled="dialogType === 'view'" />
        </el-form-item>
        <el-form-item label="上级分类" prop="fcid" v-if="dialogType === 'add' && parentData">
          <el-input :value="parentData.cname" disabled placeholder="上级分类" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button v-if="dialogType !== 'view'" type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ dialogType === "add" ? "新增" : "保存" }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="treeProTable">
import { ref, reactive, onMounted, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useHandleData } from "@/hooks/useHandleData";

import { getCategoryList, addCategory, updateCategory, deleteCategory } from "@/api/modules/category";

// 树形组件引用
const treeRef = ref();
const formRef = ref();

// 搜索相关
const searchText = ref("");

// 树形数据
const treeData = ref([]);
const treeProps = {
  children: "children",
  label: "cname"
};

// 对话框相关
const dialogVisible = ref(false);
const dialogType = ref("add"); // add, edit, view
const dialogTitle = ref("");
const formLoading = ref(false);
const submitLoading = ref(false);
const parentData = ref(null);

// 表单数据
const formData = reactive({
  cid: null,
  cname: "",
  idname: "",
  fcid: 0,
  createTime: null,
  updateTime: null
});

// 表单验证规则
const formRules = {
  cname: [
    { required: true, message: "请输入分类名称", trigger: "blur" },
    { min: 2, max: 100, message: "分类名称长度在 2 到 100 个字符", trigger: "blur" }
  ]
};

// 获取树形数据
const getTreeData = async () => {
  try {
    console.log("开始获取分类树数据...");
    formLoading.value = true;

    const response = await getCategoryList();
    console.log("API响应数据:", response);

    // 处理后端返回的数据格式
    let processedData = [];
    if (response && response.data) {
      processedData = Array.isArray(response.data) ? response.data : [response.data];
    } else if (Array.isArray(response)) {
      processedData = response;
    }

    console.log("处理后的数据:", processedData);
    treeData.value = processedData;

    if (processedData.length > 0) {
      ElMessage.success(`数据加载成功，共 ${processedData.length} 个根节点`);
    } else {
      ElMessage.warning("暂无数据");
    }
  } catch (error) {
    console.error("数据加载失败:", error);
    ElMessage.error("数据加载失败：" + (error.message || "未知错误"));
    treeData.value = [];
  } finally {
    formLoading.value = false;
  }
};

// 刷新树形数据
const refreshTree = () => {
  getTreeData();
};

// 搜索过滤
const filterNode = (value, data) => {
  if (!value) return true;
  return data.cname.toLowerCase().includes(value.toLowerCase()) ||
         (data.idname && data.idname.toLowerCase().includes(value.toLowerCase()));
};

// 处理搜索
const handleSearch = value => {
  treeRef.value?.filter(value);
};

// 展开全部
const expandAll = () => {
  const nodes = treeRef.value?.store?.nodesMap;
  if (nodes) {
    Object.values(nodes).forEach(node => {
      node.expanded = true;
    });
  }
};

// 收起全部
const collapseAll = () => {
  const nodes = treeRef.value?.store?.nodesMap;
  if (nodes) {
    Object.values(nodes).forEach(node => {
      node.expanded = false;
    });
  }
};

// 节点点击事件
const handleNodeClick = data => {
  console.log("节点点击:", data);
};

// 打开对话框
const openDialog = (type, data = null) => {
  dialogType.value = type;
  parentData.value = null;

  switch (type) {
    case "add":
      dialogTitle.value = data ? `添加子分类 - ${data.cname}` : "新增分类";
      resetForm();
      if (data) {
        parentData.value = data;
        formData.fcid = data.cid;
      }
      break;
    case "edit":
      dialogTitle.value = `编辑分类 - ${data.cname}`;
      Object.assign(formData, data);
      break;
    case "view":
      dialogTitle.value = `查看分类 - ${data.cname}`;
      Object.assign(formData, data);
      break;
  }

  dialogVisible.value = true;
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    cid: null,
    cname: "",
    idname: "",
    fcid: 0,
    createTime: null,
    updateTime: null
  });
  formRef.value?.clearValidate();
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    submitLoading.value = true;

    if (dialogType.value === "add") {
      await addCategory(formData);
      ElMessage.success("新增成功");
    } else if (dialogType.value === "edit") {
      await updateCategory(formData.cid, formData);
      ElMessage.success("编辑成功");
    }

    dialogVisible.value = false;
    await getTreeData();
  } catch (error) {
    if (error.message) {
      ElMessage.error("操作失败：" + error.message);
    }
  } finally {
    submitLoading.value = false;
  }
};

// 删除节点
const handleDelete = async data => {
  try {
    await ElMessageBox.confirm(
      `确认删除分类 "${data.cname}" 吗？${
        data.children && data.children.length > 0 ? "注意：删除后其所有子分类也将被删除！" : ""
      }`,
      "删除确认",
      {
        type: "warning",
        confirmButtonText: "确认删除",
        cancelButtonText: "取消"
      }
    );

    await useHandleData(deleteCategory, data.cid, `删除分类【${data.cname}】`);
    await getTreeData();
  } catch (error) {
    // 用户取消删除或删除失败
    if (error !== "cancel") {
      ElMessage.error("删除失败：" + error.message);
    }
  }
};

// 导出数据
const exportData = async () => {
  try {
    ElMessage.info("导出功能开发中...");
    // TODO: 实现导出功能
  } catch (error) {
    ElMessage.error("导出失败：" + error.message);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  console.log("组件挂载，开始获取分类数据...");
  getTreeData();
});
</script>

<style scoped lang="scss">
.main-box {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 16px;

  .search-box {
    .search-content {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;
    }
  }

  .tree-box {
    flex: 1;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .header-actions {
        display: flex;
        gap: 8px;
      }
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;
      color: #909399;

      .el-icon {
        font-size: 24px;
        margin-bottom: 12px;
      }
    }

    .empty-container {
      padding: 20px;
    }

    .category-tree {
      max-height: calc(100vh - 300px);
      overflow-y: auto;

      .tree-node {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 4px 8px;
        border-radius: 4px;
        transition: background-color 0.3s;

        &:hover {
          background-color: #f5f7fa;

          .node-actions {
            opacity: 1;
          }
        }

        .node-content {
          display: flex;
          align-items: center;
          gap: 8px;
          flex: 1;
          min-width: 0;

          .node-icon {
            display: flex;
            align-items: center;
            color: #909399;
          }

          .node-label-container {
            display: flex;
            flex-direction: column;
            gap: 2px;
            flex: 1;
            min-width: 0;

            .node-label {
              font-size: 14px;
              color: #303133;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              font-weight: 500;
            }

            .node-idname {
              font-size: 12px;
              color: #909399;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              font-style: italic;
            }
          }

          .node-info {
            display: flex;
            gap: 4px;
            margin-left: 8px;
          }
        }

        .node-actions {
          display: flex;
          gap: 4px;
          opacity: 0;
          transition: opacity 0.3s;

          .el-button {
            padding: 4px 8px;
            font-size: 12px;
            height: auto;
            line-height: 1;
          }
        }
      }
    }
  }
}

// 对话框样式
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 树形组件自定义样式
:deep(.el-tree) {
  .el-tree-node__content {
    height: auto;
    padding: 0;
  }

  .el-tree-node__expand-icon {
    color: #409eff;
  }

  .el-tree-node__label {
    width: 100%;
  }
}

// 卡片样式优化
:deep(.el-card) {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
  }

  .el-card__body {
    padding: 20px;
  }
}

// 表单样式优化
:deep(.el-form) {
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .main-box {
    .search-box {
      .search-content {
        flex-direction: column;
        align-items: stretch;

        .el-input {
          width: 100% !important;
          margin-right: 0 !important;
          margin-bottom: 12px;
        }
      }
    }

    .tree-box {
      .category-tree {
        .tree-node {
          .node-actions {
            opacity: 1;
            flex-direction: column;
            gap: 2px;

            .el-button {
              font-size: 10px;
              padding: 2px 6px;
            }
          }
        }
      }
    }
  }
}
</style>
