import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

// 获取所有产品树形数据
export const getFproductList = params => {
  return http.get(PORT1 + `/f_product/all`, params);
};

// 获取单个产品详情
export const getFproductDetail = id => {
  return http.get(PORT1 + `/f_product/${id}`);
};

// 新增产品
export const addFproduct = params => {
  return http.post(PORT1 + `/f_product/category`, params);
};

// 编辑产品
export const editFproduct = params => {
  return http.post(PORT1 + `/f_product/update/${params.fid}`, params);
};

// 删除产品
export const deleteFproduct = id => {
  return http.get(PORT1 + `/f_product/delete/${id}`);
};

// 批量删除产品
export const batchDeleteFproduct = ids => {
  return http.delete(PORT1 + `/f_product/batch`, { data: { ids } });
};

// 搜索产品
export const searchFproduct = params => {
  return http.get(PORT1 + `/f_product/search`, params);
};

// 移动产品节点
export const moveFproduct = params => {
  return http.put(PORT1 + `/f_product/move`, params);
};
