<template>
  <el-dialog
    v-model="dialogVisible"
    title="日志详情"
    width="800px"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <el-descriptions :column="2" border>
      <el-descriptions-item label="日志ID" :span="1">{{ logData.id }}</el-descriptions-item>
      <el-descriptions-item label="用户ID" :span="1">{{ logData.userId }}</el-descriptions-item>
      <el-descriptions-item label="用户名" :span="1">{{ logData.username }}</el-descriptions-item>
      <el-descriptions-item label="功能模块" :span="1">{{ logData.module }}</el-descriptions-item>
      <el-descriptions-item label="操作内容" :span="2">{{ logData.operation }}</el-descriptions-item>
      <el-descriptions-item label="方法" :span="2">{{ logData.method }}</el-descriptions-item>
      <el-descriptions-item label="请求URL" :span="1">{{ logData.requestUrl }}</el-descriptions-item>
      <el-descriptions-item label="请求方式" :span="1">
        <el-tag :type="getMethodTagType(logData.requestMethod)">{{ logData.requestMethod }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="操作状态" :span="1">
        <el-tag :type="logData.status === 1 ? 'success' : 'danger'">
          {{ logData.status === 1 ? '成功' : '失败' }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="操作时间" :span="1">{{ formatDateTime(logData.createTime) }}</el-descriptions-item>
      <el-descriptions-item label="操作IP" :span="1">{{ logData.ip }}</el-descriptions-item>
      <el-descriptions-item label="IP归属地" :span="1">{{ logData.ipLocation }}</el-descriptions-item>
      <el-descriptions-item label="用户代理" :span="2">{{ logData.userAgent }}</el-descriptions-item>
      <el-descriptions-item label="请求参数" :span="2">
        <div class="code-block">
          <pre>{{ formatJson(logData.requestParam) }}</pre>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="响应数据" :span="2">
        <div class="code-block">
          <pre>{{ formatJson(logData.responseData) }}</pre>
        </div>
      </el-descriptions-item>
      <el-descriptions-item v-if="logData.errorMsg" label="错误信息" :span="2">
        <div class="error-block">
          <pre>{{ logData.errorMsg }}</pre>
        </div>
      </el-descriptions-item>
    </el-descriptions>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="copyLogInfo">复制信息</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import { formatDateTime } from "@/utils/date";

// 对话框可见性
const dialogVisible = ref(false);

// 日志数据
const logData = reactive({
  id: null,
  userId: null,
  username: "",
  module: "",
  operation: "",
  method: "",
  requestUrl: "",
  requestMethod: "",
  requestParam: "",
  responseData: "",
  ip: "",
  ipLocation: "",
  userAgent: "",
  status: 1,
  errorMsg: "",
  createTime: ""
});

/**
 * 获取请求方法标签类型
 */
const getMethodTagType = (method) => {
  const typeMap = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger',
    'PATCH': 'info'
  };
  return typeMap[method] || 'info';
};

/**
 * 格式化JSON字符串
 */
const formatJson = (jsonString) => {
  if (!jsonString) return "";
  try {
    const obj = typeof jsonString === 'string' ? JSON.parse(jsonString) : jsonString;
    return JSON.stringify(obj, null, 2);
  } catch (e) {
    return jsonString;
  }
};

/**
 * 复制日志信息
 */
const copyLogInfo = () => {
  const text = `
日志ID: ${logData.id}
用户ID: ${logData.userId}
用户名: ${logData.username}
功能模块: ${logData.module}
操作内容: ${logData.operation}
方法: ${logData.method}
请求URL: ${logData.requestUrl}
请求方式: ${logData.requestMethod}
操作状态: ${logData.status === 1 ? '成功' : '失败'}
操作时间: ${formatDateTime(logData.createTime)}
操作IP: ${logData.ip}
IP归属地: ${logData.ipLocation}
用户代理: ${logData.userAgent}
请求参数: ${formatJson(logData.requestParam)}
响应数据: ${formatJson(logData.responseData)}
${logData.errorMsg ? `错误信息: ${logData.errorMsg}` : ''}
  `.trim();

  navigator.clipboard.writeText(text)
    .then(() => {
      ElMessage.success("日志信息已复制到剪贴板");
    })
    .catch(() => {
      ElMessage.error("复制失败，请手动复制");
    });
};

/**
 * 打开对话框
 */
const open = (data) => {
  // 复制数据到响应式对象
  Object.assign(logData, data);
  dialogVisible.value = true;
};

// 暴露方法给父组件
defineExpose({
  open
});
</script>

<style scoped>
.code-block {
  max-height: 200px;
  overflow-y: auto;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 8px;
}

.error-block {
  max-height: 200px;
  overflow-y: auto;
  background-color: #fef0f0;
  border-radius: 4px;
  padding: 8px;
  color: #f56c6c;
}

pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
