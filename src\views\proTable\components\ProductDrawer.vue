<template>
  <el-drawer v-model="drawerVisible" :destroy-on-close="true" size="450px" :title="`${drawerProps.title}商品`">
    <template #default>
      <el-form
        ref="ruleFormRef"
        label-width="100px"
        label-suffix=" :"
        :rules="rules"
        :disabled="drawerProps.isView"
        :model="drawerProps.row"
        :hide-required-asterisk="drawerProps.isView"
      >
        <el-form-item label="条码" prop="barCode">
          <el-input v-model="drawerProps.row.barCode" placeholder="请输入商品条码" clearable></el-input>
        </el-form-item>
        <el-form-item label="品牌" prop="brand">
          <el-input v-model="drawerProps.row.brand" placeholder="请输入商品品牌" clearable></el-input>
        </el-form-item>
        <el-form-item label="分类" prop="typeName">
          <el-input v-model="drawerProps.row.typeName" placeholder="请输入商品分类" clearable></el-input>
        </el-form-item>
        <el-form-item label="商品编码" prop="productCode">
          <el-input v-model="drawerProps.row.productCode" placeholder="请输入商品编码" clearable></el-input>
        </el-form-item>
        <el-form-item label="商品名称" prop="productName">
          <el-input v-model="drawerProps.row.productName" placeholder="请输入商品名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="商品SKU" prop="sku">
          <el-input v-model="drawerProps.row.sku" placeholder="请输入商品SKU" clearable></el-input>
        </el-form-item>
        <el-form-item label="商品状态" prop="status">
          <el-select
            v-model="drawerProps.row.status"
            placeholder="请选择商品状态"
            clearable
            style="width: 100%"
            :teleported="false"
            @change="handleStatusChange"
          >
            <el-option label="在售" :value="0"></el-option>
            <el-option label="下架" :value="1"></el-option>
            <el-option label="缺货" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标准售价" prop="price">
          <el-input-number v-model="drawerProps.row.price" :precision="2" :min="0" placeholder="请输入标准售价"></el-input-number>
        </el-form-item>
        <el-form-item label="优惠价格1" prop="priceOne">
          <el-input-number v-model="drawerProps.row.priceOne" :precision="2" :min="0" placeholder="请输入优惠价格1"></el-input-number>
        </el-form-item>
        <el-form-item label="优惠价格2" prop="priceTwo">
          <el-input-number v-model="drawerProps.row.priceTwo" :precision="2" :min="0" placeholder="请输入优惠价格2"></el-input-number>
        </el-form-item>
        <el-form-item label="优惠价格3" prop="priceThree">
          <el-input-number v-model="drawerProps.row.priceThree" :precision="2" :min="0" placeholder="请输入优惠价格3"></el-input-number>
        </el-form-item>
        <el-form-item label="优惠价格4" prop="priceFour">
          <el-input-number v-model="drawerProps.row.priceFour" :precision="2" :min="0" placeholder="请输入优惠价格4"></el-input-number>
        </el-form-item>
        <el-form-item label="优惠价格5" prop="priceFive">
          <el-input-number v-model="drawerProps.row.priceFive" :precision="2" :min="0" placeholder="请输入优惠价格5"></el-input-number>
        </el-form-item>
        <el-form-item label="库存" prop="inventory">
          <el-input-number v-model="drawerProps.row.inventory" :min="0" placeholder="请输入库存数量"></el-input-number>
        </el-form-item>
        <el-form-item label="描述" prop="describe">
          <el-input
            v-model="drawerProps.row.describe"
            type="textarea"
            :rows="3"
            placeholder="请输入商品描述"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <el-button @click="drawerVisible = false">取消</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-drawer>
</template>

<script setup name="ProductDrawer">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { getProductCategories } from "@/api/modules/product";

const rules = reactive({
  productCode: [{ required: true, message: "请输入商品编码", trigger: "blur" }],
  productName: [{ required: true, message: "请输入商品名称", trigger: "blur" }],
  brand: [{ required: true, message: "请输入商品品牌", trigger: "blur" }],
  hierarchy: [{ required: true, message: "请选择商品分类", trigger: "change" }],
  status: [{ required: true, message: "请选择商品状态", trigger: "change" }],
  price: [{ required: true, message: "请输入标准售价", trigger: "blur" }]
});

// 抽屉属性类型定义
const drawerPropsType = {
  title: String,
  isView: Boolean,
  row: Object,
  api: Function,
  getTableList: Function
};

const drawerVisible = ref(false);
const drawerProps = ref({
  isView: false,
  title: "",
  row: {}
});

// 分类选项
const categoryOptions = ref([]);

// 获取分类数据
const getCategoryOptions = async () => {
  try {
    const { data } = await getProductCategories();
    // 递归处理分类数据
    const processCategories = (categories, level = 0) => {
      const result = [];
      if (Array.isArray(categories)) {
        categories.forEach(category => {
          result.push({
            label: `${'  '.repeat(level)}${category.fname}`,
            value: category.fid
          });
          if (category.children && category.children.length > 0) {
            result.push(...processCategories(category.children, level + 1));
          }
        });
      }
      return result;
    };
    
    categoryOptions.value = data ? processCategories([data]) : [];
  } catch (error) {
    console.error('获取分类数据失败:', error);
    categoryOptions.value = [];
  }
};

// 状态变化处理
const handleStatusChange = (value) => {
  console.log('商品状态变化:', value);
  drawerProps.value.row.status = value;
};

// 接收父组件参数
const acceptParams = (params) => {
  drawerProps.value = params;
  // 确保row对象存在并初始化status字段
  if (!drawerProps.value.row) {
    drawerProps.value.row = {};
  }
  // 如果status字段不存在，初始化为0（在售）
  if (drawerProps.value.row.status === undefined || drawerProps.value.row.status === null) {
    drawerProps.value.row.status = 0;
  }
  drawerVisible.value = true;
};

// 提交数据
const ruleFormRef = ref();
const handleSubmit = () => {
  ruleFormRef.value.validate(async (valid) => {
    if (!valid) return;
    try {
      await drawerProps.value.api(drawerProps.value.row);
      ElMessage.success({ message: `${drawerProps.value.title}商品成功！` });
      drawerProps.value.getTableList();
      drawerVisible.value = false;
    } catch (error) {
      console.log(error);
    }
  });
};

onMounted(() => {
  getCategoryOptions();
});

defineExpose({
  acceptParams
});
</script>

<style scoped>
/* 确保下拉选择框正常工作 */
.el-select {
  width: 100%;
}

/* 修复可能的z-index问题 */
:deep(.el-select-dropdown) {
  z-index: 9999 !important;
}

/* 确保下拉框可以正常点击 */
:deep(.el-select__wrapper) {
  cursor: pointer;
}

:deep(.el-select__placeholder) {
  color: #a8abb2;
}
</style>
